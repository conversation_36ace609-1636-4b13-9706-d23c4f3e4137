package import_csv

import (
	"flag"
	"fmt"
	"os"
	"strings"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/tron-us/go-common/constant"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/common"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/config"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/database/postgres"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/logic"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/mock"

	log "github.com/sirupsen/logrus"
	"go.uber.org/zap"
)

func parseFlag() (string, string) {
	fmt.Println("")

	filePath := flag.String("file_path", "", "../data/input/AirdropNew-20221024.result.csv")
	epochDay := flag.String("epoch_day", "", "2022-04-01")
	flag.Usage = func() {
		fmt.Println("Usage: flagdemo [-file_path string] [-epoch_day string]")
		flag.PrintDefaults()
	}
	flag.Parse()

	if len(*filePath) <= 0 || len(*epochDay) <= 0 {
		flag.Usage()
		return "", "'"
	}

	fmt.Println("file_path:", *filePath)
	fmt.Println("epoch_day:", *epochDay)

	return *filePath, *epochDay
}

func ImportCsv(filePath, epochDay string) error {
	// 1. 从 s3 上读取文件
	sess, err := session.NewSession(&aws.Config{
		// Region:   aws.String(config.AwsS3Config.Region),
		LogLevel: aws.LogLevel(aws.LogDebug),
	})

	if err != nil {
		return err
	}

	// credentials := stscreds.NewCredentials(sess, config.AwsS3Config.Role)
	// s3Client := s3.New(sess, &aws.Config{Credentials: credentials, Region: aws.String(config.AwsS3Config.Region)})
	s3Client := s3.New(sess, &aws.Config{Region: aws.String(config.AwsS3Config.Region)})

	downloader := s3manager.NewDownloader(sess)
	downloader.S3 = s3Client

	fileName := fmt.Sprintf("AirdropNewBttc-%s.csv", strings.Join(strings.Split(epochDay, "-"), ""))

	tempFile := filePath + "/" + fileName

	// 判断文件是否存在
	if _, err := os.Stat(tempFile); err == nil {
		fmt.Println("文件已存在:", tempFile)
	} else {

		file, err := os.Create(tempFile)
		if err != nil {
			fmt.Println("创建文件失败:", err)
			return err
		}

		defer file.Close()

		// 下载文件
		numBytes, err := downloader.Download(file, &s3.GetObjectInput{
			Bucket: aws.String(config.AwsS3Config.BucketName),
			Key:    aws.String(config.AwsS3Config.StaticPrefix + fileName),
		})

		if err != nil {
			fmt.Println("下载文件失败:", err)
			return err
		}

		fmt.Println("下载成功，文件大小:", numBytes, "bytes")
	}

	// 2. 解析文件将数据写表
	// filePath, epochDay := parseFlag()

	log.Info("import begin...", common.NowTime())

	// init db
	if err := postgres.PGDBWrite.Ping(); err != nil {
		log.Error(constant.DBWriteConnectionError, zap.Error(err))
		return err
	}
	log.Info("PostgreSQL is running",
		zap.String("user", postgres.PGDBWrite.Options().User),
		zap.String("addr", postgres.PGDBWrite.Options().Addr),
		zap.String("db", postgres.PGDBWrite.Options().Database))

	// record origin
	epoch, err := logic.InsertOriginProcess(tempFile, epochDay)
	if err != nil {
		if epoch == -1 {
			log.Error("AirdropProcess err, ", err.Error())
			return err
		}

		log.Error("AirdropProcess err, ", zap.Error(err))
		return err
	}

	if config.BttcConfig.EnableMock {
		err = mock.DistributeToMockUsers(epochDay, epoch)
		if err != nil {
			log.Error("DistributeToMockUsers err, ", zap.Error(err))
		}
	}

	log.Info("import end...", common.NowTime())
	return nil
}
