package service

import (
	"bufio"
	"context"
	"encoding/csv"
	"fmt"
	"log"
	"math/big"
	"os"
	"strconv"
	"time"

	"github.com/ethereum/go-ethereum/ethclient"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/bttc"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/config"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/dal"
	util "gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/utils"
	"gopkg.in/gomail.v2"
)

func FinanceCheck(period int, now time.Time) {
	days := make([]string, period)
	for i := 0; i < period; i++ {
		// 要获取昨天的领取记录
		days[i] = now.AddDate(0, 0, -i).Format("2006-01-02")
	}
	// 查询 merkle root
	// airdrops, err := dal.GetAirDropByDays(days)
	// if err != nil {
	// 	util.SendTelegramMessage(fmt.Sprintf("❌ 生成对账 csv 文件失败, 失败原因: %s", err.Error()))
	// 	return
	// }
	//
	// if len(airdrops) == 0 {
	// 	util.SendTelegramMessage("❌ 生成对账 csv 文件失败，失败原因: 未查询到空投记录")
	// 	return
	// }
	//
	// merkleRoots := make([]string, len(airdrops))
	// for i, a := range airdrops {
	// 	merkleRoots[i] = a.MerkleRoot
	// }

	// claimedAmount, err := exportToCsv(days)
	// if err != nil {
	// util.SendTelegramMessage(fmt.Sprintf("❌ 生成对账 csv 文件失败, 失败原因: %s", err.Error()))
	// return
	// }

	// 2. 发送文件
	// err = util.SendFile(getFileName(days))
	// if err != nil {
	// util.SendTelegramMessage(fmt.Sprintf("❌ 发送对账 csv 文件失败, 失败原因: %s", err.Error()))
	// }

	// 3. 调用 api 获取该周期内合约的余额
	st, _ := time.Parse("2006-01-02", days[len(days)-1])
	st = time.Date(st.Year(), st.Month(), st.Day(), 0, 0, 0, 0, time.UTC)

	beginBalance, startBlockNum, err := util.GetAirdropContractBalance(st.Unix())
	if err != nil {
		util.SendTelegramMessage(fmt.Sprintf("❌ 获取合约余额失败, 失败原因: %s", err.Error()))
		return
	}
	// 结束时的余额
	et, _ := time.Parse("2006-01-02", days[0])
	et = time.Date(et.Year(), et.Month(), et.Day(), 0, 0, 0, 0, time.UTC)

	endTime := et.AddDate(0, 0, 1).Unix()
	if endTime > time.Now().Unix() {
		endTime = time.Now().Unix()
	}
	endBalance, endBlockNum, err := util.GetAirdropContractBalance(endTime) // 要计算结束那一天的23:59:59，加一天得到零时

	if err != nil {
		util.SendTelegramMessage(fmt.Sprintf("❌ 获取合约余额失败, 失败原因: %s", err.Error()))
		return
	}

	// 扫块 获取 充值金额
	amount, totalClaimed, err := bttc.StartBttcListener(startBlockNum, endBlockNum)
	if err != nil {
		util.SendTelegramMessage(fmt.Sprintf("❌ 获取合约余额失败, 失败原因: %s", err.Error()))
		return
	}
	// 4. 计算判断账是否是正确的
	// 空投总量
	totalAirdropAmount := amount

	tempTotal := new(big.Int).Set(totalAirdropAmount)
	tempBegin := new(big.Int).Set(beginBalance)
	tempEnd := new(big.Int).Set(endBalance)
	tempClaimed := new(big.Int).Set(totalClaimed)

	input := totalAirdropAmount.Add(totalAirdropAmount, beginBalance)
	output := endBalance.Add(endBalance, totalClaimed)

	// beginBalance + totalAirdropAmount - endBalance - claimedAmount = 0
	if input.Cmp(output) == 0 {
		util.SendTelegramMessage(fmt.Sprintf("✅ 对账周期[%s, %s], 对账正确: \n"+
			"该对账周期内总的应发空投总额为 %d\n"+
			"该对账周期开始时合约中的余额为 %d\n"+
			"该对账周期结束时合约中的余额为 %d\n"+
			"该对账周期内用户领取的总额为 %d",
			days[len(days)-1], days[0],
			tempTotal, tempBegin, tempEnd, tempClaimed))
		return
	}
	util.SendTelegramMessage(fmt.Sprintf("❌ 对账周期[%s, %s], 对账错误: \n"+
		"该对账周期内总的应发空投总额为 %d\n"+
		"该对账周期开始时合约中的余额为 %d\n"+
		"该对账周期结束时合约中的余额为 %d\n"+
		"该对账周期内用户领取的总额为 %d",
		days[len(days)-1], days[0],
		tempTotal, tempBegin, tempEnd, tempClaimed))
	return
}

func getFileName(days []string) string {
	start := days[0]
	end := days[len(days)-1]

	from, _ := time.Parse("2006-01-02", end)
	to, _ := time.Parse("2006-01-02", start)

	return fmt.Sprintf(config.FileConfig.CsvFilePath+"/"+"airdrop-finance-%s-%s.csv", from.Format("20060102"), to.Format("20060102"))
}

func exportToCsv(days []string) (*big.Int, error) {
	// 1. 查询 bttc_events表  获取某个时间周期内的领取记录
	file, err := os.Create(getFileName(days))
	if err != nil {
		return &big.Int{}, fmt.Errorf("创建 CSV 文件失败: %w", err)
	}
	defer file.Close()

	bufferedWriter := bufio.NewWriter(file)
	writer := csv.NewWriter(bufferedWriter)

	header := []string{"ID", "Block Number", "Log Index", "Tx Hash", "Block Time", "Block Date", "Sender Address", "Name", "Contract Address", "Merkle Root", "To Address", "Amount"}
	if err = writer.Write(header); err != nil {
		return &big.Int{}, fmt.Errorf("写入 CSV 头部失败: %w", err)
	}

	pageSize := 5000 // 每次查询 5000 条，避免一次性加载大量数据
	pageNum := 1

	claimedAmount := new(big.Int)

	for {
		events, err := dal.ListClaimedEvents(pageSize, pageNum, config.BttcConfig.ProxyContractAddress, days)
		if err != nil {
			return &big.Int{}, err
		}
		// 将 event 解析写入到 csv 文件中
		records := make([][]string, len(events))
		for i, event := range events {
			records[i] = []string{
				fmt.Sprintf("%d", event.ID),
				strconv.FormatUint(event.BlockNumber, 10),
				strconv.FormatUint(uint64(*event.LogIndex), 10),
				event.TxHash,
				event.BlockTime.Format("2006-01-02 15:04:05"),
				event.BlockDate.Format("2006-01-02 15:04:05"),
				event.SenderAddr,
				event.Name,
				event.ContractAddr,
				event.MerkleRoot,
				event.ToAddr,
				event.Amount,
			}
			amount := new(big.Int)
			amount.SetString(event.Amount, 10)
			claimedAmount.Add(claimedAmount, amount)
		}
		if err := writer.WriteAll(records); err != nil {
			return &big.Int{}, fmt.Errorf("写入 CSV 失败: %w", err)
		}

		bufferedWriter.Flush()

		if len(events) == 0 {
			break
		}
		pageNum += 1
	}

	writer.Flush()
	bufferedWriter.Flush()
	return claimedAmount, nil
}

func sendEmailWithAttachment(attachmentPath string) error {
	// **Gmail SMTP 服务器**
	smtpHost := "smtp.gmail.com"
	smtpPort := 587
	senderEmail := "<EMAIL>"
	senderPassword := "your-app-password"
	toEmails := []string{""}

	// 创建邮件
	m := gomail.NewMessage()
	m.SetHeader("From", senderEmail)
	m.SetHeader("To", toEmails...)
	m.SetHeader("Subject", "Airdrop Finance Check CSV")
	m.SetBody("text/plain", "请查看附件中的 CSV 文件。")
	m.Attach(attachmentPath)

	// 连接 SMTP 服务器
	d := gomail.NewDialer(smtpHost, smtpPort, senderEmail, senderPassword)

	// 发送邮件
	if err := d.DialAndSend(m); err != nil {
		return fmt.Errorf("发送邮件失败: %w", err)
	}

	fmt.Println("邮件发送成功！")
	return nil
}

var monitorStartBlockNum = uint64(0)
var latestAirdropDay string

func init() {
	var err error
	monitorStartBlockNum, err = getCurrentBlockNum()
	if err != nil {
		fmt.Println("getCurrentBlockNum error: ", err)
		panic(err)
	}
	log.Printf("monitorStartBlockNum: %d", monitorStartBlockNum)

	getLatestAirdropDay()
	log.Printf("latestAirdropDay: %s", latestAirdropDay)
}

func getCurrentBlockNum() (uint64, error) {
	// 连接到以太坊节点
	client, err := ethclient.Dial(config.BttcConfig.BttcAddress)
	if err != nil {
		return 0, fmt.Errorf("连接到以太坊节点失败: %w", err)
	}
	defer client.Close()
	// 获取当前区块号
	currentBlockNum, err := client.BlockNumber(context.Background())
	if err != nil {
		return 0, fmt.Errorf("获取当前区块号失败: %w", err)
	}
	return currentBlockNum, nil
}

func getLatestAirdropDay() {
	airdropDay, err := dal.GetLatestAirdropDay()
	if err != nil {
		fmt.Println("GetLatestAirdropDay error: ", err)
		return
	}
	latestAirdropDay = airdropDay.EpochDay
}

func TransferEventMonitor() {
	amount, blockNum, err := bttc.StartMonitor(monitorStartBlockNum)
	if err != nil {
		fmt.Println("StartMonitor error: ", err)
		return
	}
	monitorStartBlockNum = blockNum

	if amount.Cmp(big.NewInt(0)) == 0 {
		return
	}

	amount = amount.Div(amount, big.NewInt(1000000000000000000)) // 转换为 BTT，1 BTT = 10^18 wei

	dailyAmount := new(big.Int).Mul(
		big.NewInt(75),
		big.NewInt(100000000), // 亿
	)

	days := new(big.Int).Div(amount, dailyAmount)

	fmt.Printf("当前充值金额 %s 相当于 %d 天的空投额度\n", amount.String(), days)

	if latestAirdropDay == "" {
		// 从数据库中获取当前是空投到几号了
		airdropDay, err := dal.GetLatestAirdropDay()
		if err != nil {
			fmt.Println("GetLatestAirdropDay error: ", err)
			return
		}
		latestAirdropDay = airdropDay.EpochDay
	}

	// 计算应该空投的日期范围
	// 2025-04-19
	lastDay, err := time.Parse("2006-01-02", latestAirdropDay)
	if err != nil {
		fmt.Println("parse time error", err)
	}

	startDate := lastDay.AddDate(0, 0, 1)
	startDay := startDate.Format("2006-01-02")
	if days.Int64() > 1 {
		endDate := lastDay.AddDate(0, 0, int(days.Int64()))
		endDay := endDate.Format("2006-01-02")

		util.SendTelegramMessage(fmt.Sprintf("✅ 监控到有 %s 个 BTT 转入，应该空投第 %s 天到第 %s 天的空投", amount.String(), startDay, endDay))
		latestAirdropDay = endDay
		return
	}

	util.SendTelegramMessage(fmt.Sprintf("✅ 监控到有 %s 个 BTT 转入，应该空投 %s ", amount.String(), startDay))
	latestAirdropDay = startDay
}
