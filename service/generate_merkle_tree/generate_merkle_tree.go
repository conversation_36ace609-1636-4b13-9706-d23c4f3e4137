package generate_merkle_tree

import (
	"flag"
	"fmt"
	"math/rand"
	"time"

	"github.com/tron-us/go-common/constant"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/common"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/database/postgres"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/logic"

	log "github.com/sirupsen/logrus"
	"go.uber.org/zap"
)

func parseFlag() string {
	fmt.Println("")

	epochDay := flag.String("epoch_day", "", "2022-04-01")
	flag.Usage = func() {
		fmt.Println("Usage: flagdemo [-epoch_day string]")
		flag.PrintDefaults()
	}
	flag.Parse()

	if len(*epochDay) <= 0 {
		flag.Usage()
		return ""
	}
	fmt.Println("epoch_day:", *epochDay)

	return *epochDay
}

func GenerateMerkleTree(epochDay string) error {
	log.Info("generate merkle root, begin ...", common.NowTime())

	// epochDay := parseFlag()
	rand.Seed(time.Now().UnixNano())

	// init db
	if err := postgres.PGDBWrite.Ping(); err != nil {
		log.Error(constant.DBWriteConnectionError, zap.Error(err))
	}
	log.Info("PostgreSQL is running",
		zap.String("user", postgres.PGDBWrite.Options().User),
		zap.String("addr", postgres.PGDBWrite.Options().Addr),
		zap.String("db", postgres.PGDBWrite.Options().Database))

	// airdrop process
	count, err := logic.ProcessV2(epochDay)
	if err != nil {
		if count == -1 {
			log.Error("AirdropProcess err, ", err.Error())
			return err
		}

		log.Error("AirdropProcess err, ", zap.Error(err))
		return err
	}

	log.Info("generate merkle root, end...")
	return nil
}
