package service

import (
	"errors"
	"fmt"
	"time"

	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/bttc"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/config"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/dal"
	util "gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/utils"
	"golang.org/x/sync/errgroup"
)

func PreCheckMerkleProof(epochDay string) {
	defer func() {
		if err := recover(); err != nil {
			util.SendTelegramMessage(fmt.Sprintf("%s merkle proof check 失败, 失败原因：%v", "❌", err))
		}
	}()
	err := merkleProofCheck(epochDay)
	if err != nil {
		util.SendTelegramMessage(fmt.Sprintf("%s merkle proof check 失败, 失败原因：%s", "❌", err.Error()))
		return
	}
	util.SendTelegramMessage(fmt.Sprintf("%s %s, %s", "✅", epochDay, "Merkle proof check success"))
}

func merkleProofCheck(epochDay string) error {
	checkPercent := config.BttcConfig.CheckBatchPercent

	count, err := dal.GetAirdropStatsCountByEpoch(epochDay)
	if err != nil {
		fmt.Println("get airdrop stats count error: ", err)
		return err
	}

	batchSize := count * checkPercent / 1000

	pageSize := 500
	pageNum := 1

	totalSize := 0

	reachMaxReq := make(chan struct{}, 1)
	reachLimited := make(chan struct{}, 1)

	for {
		select {
		case <-reachMaxReq:
			fmt.Println("reachMaxReq, sleep 5s")
			time.Sleep(5 * time.Second)
		case <-reachLimited:
			fmt.Println("reached limit, sleep 1s")
			time.Sleep(1 * time.Second)
		default:
		}
		// 1. 从数据库获取待校验数据
		airdropStats, err := dal.GetAirdropStats(epochDay, batchSize, pageSize, pageNum)
		if err != nil {
			fmt.Println("get airdrop stats error: ", err)
			return err
		}

		eg := errgroup.Group{}
		eg.SetLimit(8)
		// 2. 调用合约的验证方法进行验证
		for _, airdropStat := range airdropStats {
			as := airdropStat
			eg.Go(func() error {
				return bttc.VerifyMerkleProof(as.MerkleRoot, as.BttcAddr, as.IncreaseAmount, as.Proof)
			})
		}
		err = eg.Wait()
		// 触发了限流，通知进行 sleep
		if errors.As(err, &bttc.ReachedLimit) {
			reachLimited <- struct{}{}
		} else if err != nil {
			fmt.Println("verify merkle proof error: ", err)
			return err
		}

		totalSize += len(airdropStats)
		if totalSize%10000 == 0 {
			reachMaxReq <- struct{}{}
		}
		fmt.Println("totalSize: ", totalSize)
		if totalSize >= batchSize {
			return nil
		}

		if len(airdropStats) < pageSize {
			break
		}
		pageNum++
	}

	return nil
}
