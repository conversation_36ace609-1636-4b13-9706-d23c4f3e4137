package model

import "time"

type Airdrop struct {
	tableName   string    `pg:"airdrop,alias:t,discard_unknown_columns"`
	ID          uint64    `json:"id" pg:"id"`
	Epoch       int       `json:"epoch" pg:"epoch"`
	EpochDay    string    `json:"epoch_day" pg:"epoch_day"`
	MerkleRoot  string    `json:"merkle_root" pg:"merkle_root"`
	TokenTotal  string    `json:"token_total" pg:"token_total"`
	CreatedTime time.Time `json:"created_time" pg:"created_time"`
}

type AirdropStats struct {
	tableName      string    `pg:"airdrop_stats,alias:t,discard_unknown_columns"`
	ID             uint64    `json:"id" pg:"id"`
	Epoch          int       `json:"epoch" pg:"epoch"`
	EpochDay       string    `json:"epoch_day" pg:"epoch_day"`
	IncreaseAmount string    `json:"increase_amount" pg:"increase_amount"`
	NodeId         string    `json:"node_id" pg:"node_id"`
	BttcAddr       string    `json:"bttc_addr" pg:"bttc_addr"`
	Index          int       `json:"index" pg:"index"`
	Amount         string    `json:"amount" pg:"amount"`
	Proof          string    `json:"proof" pg:"proof"`
	MerkleRoot     string    `json:"merkle_root" pg:"merkle_root"`
	Level          int       `json:"level" pg:"level"`
	CreatedTime    time.Time `json:"created_time" pg:"created_time"`
}
