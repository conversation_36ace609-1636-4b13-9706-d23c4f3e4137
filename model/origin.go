package model

import (
	"fmt"
	"math/big"
	"time"

	"github.com/ethereum/go-ethereum/common"
	util "gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/utils"
	"golang.org/x/crypto/sha3"
)

type Origin struct {
	tableName    string    `pg:"origin,alias:t,discard_unknown_columns"`
	ID           uint64    `json:"id" pg:"id"`
	NodeId       string    `json:"node_id" pg:"node_id"`
	TronAddr     string    `json:"tron_addr" pg:"tron_addr"`
	BttcAddr     string    `json:"bttc_addr" pg:"bttc_addr"`
	EpochDay     string    `json:"epoch_day" pg:"epoch_day"`
	Epoch        int       `json:"epoch" pg:"epoch"`
	AmountBttOld int64     `json:"amount_btt_old" pg:"amount_btt_old"`
	Amount       string    `json:"amount" pg:"amount"`
	ClaimFlag    bool      `json:"claim_flag" pg:"claim_flag"`
	CreatedTime  time.Time `json:"created_time" pg:"created_time"`
	UpdatedTime  time.Time `json:"updated_time" pg:"updated_time"`
}

type AirdropMerkle struct {
	*Origin
}

func (o *AirdropMerkle) CalculateHash() ([]byte, error) {
	h := sha3.NewLegacyKeccak256()

	h.Write(common.HexToAddress(o.BttcAddr).Bytes())

	amount := new(big.Int)
	amount, ok := amount.SetString(o.Amount, 10)
	if !ok {
		panic("amount can not be converted to big.Int")
	}
	h.Write(amount.FillBytes(make([]byte, 32)))

	return h.Sum(nil), nil
}

func (o *AirdropMerkle) Equals(other util.MerkleContent) (bool, error) {
	or, ok := other.(*AirdropMerkle)
	if !ok {
		return false, fmt.Errorf("merkle node can not be converted to model")
	}
	return o.ID == or.ID, nil
}

// IDValue for sorts
func (o *AirdropMerkle) IDValue() uint64 {
	return o.ID
}

func (o *AirdropMerkle) BttcAddress() string {
	return o.BttcAddr
}

func (o *AirdropMerkle) GetAmount() string {
	return o.Amount
}
