package model

import (
	"time"

	"github.com/shopspring/decimal"
)

const (
	EventAirdrop = "Claimed"
)

type BttcEvents struct {
	ID               uint64    `json:"id"`
	BlockNumber      uint64    `json:"block_number"`
	LogIndex         *uint     `json:"log_index"`
	TxHash           string    `json:"tx_hash"`
	BlockTime        time.Time `json:"block_time"`
	BlockDate        time.Time `json:"block_date"`
	SenderAddr       string    `json:"sender_addr"`
	IsSenderAddrSc   *bool     `json:"is_sender_addr_sc"`
	ContractAddr     string    `json:"contract_addr"`
	IsContractAddrSc *bool     `json:"is_contract_addr_sc"`
	TransferAmount   string    `json:"transfer_amount"`
	PeerID           string    `json:"peer_id"`
	MerkleRoot       string    `json:"merkle_root"`
	CurrencyCode     string    `json:"currency_code"`

	Name           string          `json:"name"`
	FromAddr       string          `json:"from_addr"` // token transfer from addr
	IsFromAddrSc   *bool           `json:"is_from_addr_sc"`
	ToAddr         string          `json:"to_addr"` // token transfer to addr
	IsToAddrSc     *bool           `json:"is_to_addr_sc"`
	Amount         string          `json:"amount"`
	TokenAddr      string          `json:"token_addr"`
	Nonce          uint32          `json:"nonce"`
	IncreaseNonce  uint32          `json:"increase_nonce"`
	NodeCreateTime time.Time       `json:"node_create_time"`
	SignedTime     time.Time       `json:"signed_time"`
	Gas            decimal.Decimal `json:"gas"`
	Cid            string          `json:"cid"`
}
