package common

import (
	"fmt"
	"testing"
)

func TestAddrSwitch(t *testing.T) {
	hexAddr := ToHexAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t") // 将地址转换为 hexString
	fmt.Println("HexAddr：", hexAddr)

	hexAddr = ToHexAddress("TLEMLeC93iteyU6mzMFJbf8Ly3TCF64BCE") // 将地址转换为 hexString
	fmt.Println("HexAddr：", hexAddr)

	addr, _ := FromHexAddress("41a614f803b6fd780986a42c78ec9c7f77e6ded13c") // 将 hexString 转换为地址
	fmt.Println("Addr：", addr)
}
