package common

import (
	"fmt"
	"io/ioutil"
)

func WriteIntoFile(filename string, data []byte) error {
	var err error
	err = ioutil.WriteFile(filename, data, 0666) //写入文件(字节数组)
	if err != nil {
		fmt.Printf("ReadFromFile: WriteFile faild, err = %v\n", err)
		return err
	}
	fmt.Printf("WriteIntoFile: write %d bytes to %s! \n", len(data), filename)

	return nil
}

func ReadFromFile(filename string) (data []byte, err error) {
	data, err = ioutil.ReadFile(filename)
	if err != nil {
		fmt.Printf("ReadFromFile: file open faild, err = %v\n", err)
		return nil, err
	}

	fmt.Printf("ReadFromFile: read len(data) = %d, from %s\n", len(data), filename)
	return data, nil
}
