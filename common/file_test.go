package common

import (
	"fmt"
	"math/rand"
	"strconv"
	"testing"
	"time"
)

func TestFile(t *testing.T) {
	rand.Seed(time.Now().UnixNano())
	filename := "hello." + strconv.Itoa(rand.Intn(10000))

	err := WriteIntoFile(filename, []byte("hello"))
	if err != nil {
		fmt.Println("WriteIntoFile err = ", err)
	}
	fmt.Println("WriteIntoFile ok!")

	_, err = ReadFromFile(filename)
	if err != nil {
		fmt.Println("ReadFromFile err = ", err)
	}
	fmt.Println("ReadFromFile ok!")
}
