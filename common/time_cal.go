package common

import (
	"time"
)

//获取本周周一的日期
func GetMondayOfWeek(t time.Time, fmtStr string, zeroFlag bool) (dayStr string) {
	var dayObj time.Time
	if zeroFlag {
		dayObj = GetZeroTime(t)
	} else {
		dayObj = GetFullTime(t)
	}

	if t.Weekday() == time.Monday {
		//修改hour、min、sec = 0后格式化
		dayStr = dayObj.Format(fmtStr)
	} else {
		offset := int(time.Monday - t.Weekday())
		if offset > 0 {
			offset = -6
		}
		dayStr = dayObj.AddDate(0, 0, offset).Format(fmtStr)
	}
	return
}

//获取上周周一日期
func GetLastWeekMonday(t time.Time, fmtStr string) (timeDay time.Time, strDay string, err error) {
	monday := GetMondayOfWeek(t, fmtStr, true)
	dayObj, err := time.Parse(fmtStr, monday)
	if err != nil {
		return
	}

	timeDay = dayObj.AddDate(0, 0, -7)
	strDay = timeDay.Format(fmtStr)
	return
}

//获取上周 周日日期
func GetLastWeekSunday(t time.Time, fmtStr string) (timeDay time.Time, strDay string, err error) {
	monday := GetMondayOfWeek(t, fmtStr, false)
	dayObj, err := time.Parse(fmtStr, monday)
	if err != nil {
		return
	}

	timeDay = dayObj.AddDate(0, 0, -1)
	strDay = timeDay.Format(fmtStr)
	return
}

//获取上上周 周日日期（start）
func GetLast2Sunday(t time.Time, fmtStr string) (timeDay time.Time, strDay string, err error) {
	monday := GetMondayOfWeek(t, fmtStr, true)
	dayObj, err := time.Parse(fmtStr, monday)
	if err != nil {
		return
	}

	timeDay = dayObj.AddDate(0, 0, -8)
	strDay = timeDay.Format(fmtStr)
	return
}

//获取上周 周六日期（end）
func GetLastSaturday(t time.Time, fmtStr string) (timeDay time.Time, strDay string, err error) {
	saturday := GetMondayOfWeek(t, fmtStr, false)
	dayObj, err := time.Parse(fmtStr, saturday)
	if err != nil {
		return
	}

	timeDay = dayObj.AddDate(0, 0, -2)
	strDay = timeDay.Format(fmtStr)
	return
}

//获取某一天的0点时间
func GetZeroTime(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

func GetFullTime(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location())
}

func GetEpochValue(epochDay string) (int, error) {
	start, err := time.Parse("2006-01-02", EpochStartTime)
	if err != nil {
		return 0, err
	}

	epochTime, err := time.Parse("2006-01-02", epochDay)
	if err != nil {
		return 0, err
	}

	diff := epochTime.Sub(start)
	return int(diff.Hours() / 24), nil
}

func GetEpochValueNow() (int, error) {
	start, err := time.Parse("2006-01-02", EpochStartTime)
	if err != nil {
		return 0, err
	}

	now := time.Now()
	diff := now.Sub(start)
	return int(diff.Hours() / 24), nil
}

func NowTime() string {
	return time.Now().String()[:19]
}
