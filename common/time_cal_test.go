package common

import (
	"fmt"
	"testing"
	"time"
)

func TestTimeCal(t *testing.T) {
	now := time.Now()
	tMonday, strMonday, err := GetLastWeekMonday(now, "2006-01-02 15:04:05")
	tSunday, strSunday, err := GetLastWeekSunday(now, "2006-01-02 15:04:05")
	fmt.Println(err)

	fmt.Println(tMonday, strMonday)
	fmt.Println(tSunday, strSunday)
}

func TestTimeCalNew(t *testing.T) {
	now := time.Now()
	t2Sunday, str2Sunday, err := GetLast2Sunday(now, "2006-01-02 15:04:05")
	tSaturday, strSaturday, err := GetLastSaturday(now, "2006-01-02 15:04:05")
	fmt.Println(err)

	fmt.Println(t2Sunday, str2Sunday)
	fmt.Println(tSaturday, strSaturday)
}
