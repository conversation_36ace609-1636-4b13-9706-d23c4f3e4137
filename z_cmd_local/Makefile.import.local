default: run

# 1. open a shell, run the command for dev or production
# ...

export SS_LOG_LEVEL=INFO

# 2. import_csv
run:
	GO111MODULE=on \
	TGC_PREFIX=SS_ \
	SS_ENV=production \
	SS_DB_URL="postgresql://`whoami`@localhost:5432/db_scan" \
	SS_DB_NUM_CONNS=300 SS_DB_STMT_TIMEOUT=30 GO111MODULE=on \
	\
	go run ../service/import_csv/import_csv.go -file_path "../data_source/input/AirdropNew-20221024.result.csv" -epoch_day "2022-10-24"

#	go run ../service/import_csv/import_csv.go -file_path "../data_source/input/AirdropNew-20221024.result.csv" -epoch_day "2022-09-04"
