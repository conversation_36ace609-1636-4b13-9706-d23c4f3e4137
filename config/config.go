package config

import "gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/env"

type AirDropConfig struct {
	BttcAddress            string   `env:"BTTC_ADDRESS" default:"https://pre-rpc.bt.io/"`
	ChainID                int64    `env:"CHAIN_ID" default:"1029"`
	ProposalPrivateKey     string   `env:"PROPOSAL_PRIVATE_KEY" default:"82221d71619c5a6d679b58a21ccae502e33bd3c12ad2a4a10e8b13f817668b41"`
	ReviewPrivateKey       string   `env:"REVIEW_PRIVATE_KEY" default:"82221d71619c5a6d679b58a21ccae502e33bd3c12ad2a4a10e8b13f817668b41"`
	ProxyContractAddress   string   `env:"PROXY_CONTRACT_ADDRESS" default:"******************************************"`
	CheckBatchPercent      int      `env:"CHECK_BATCH_PERCENT" default:"1"` // 1-100
	FinanceCheckRange      int      `env:"FINANCE_CHECK_RANGE" default:"1"` // 1-30
	BTTCScanApiUrl         string   `env:"BTTC_SCAN_API_URL" default:"https://api.etherscan.io/v2/api"`
	BTTCScanApiKey         string   `env:"BTTC_SCAN_API_KEY" default:"P46IDMV5UF3HGX3FEJPD2RK5VFE1PM865Z"`
	JobAirdropExecute      string   `env:"JOB_AIRDROP_EXECUTE" default:"02:00:00"`
	JobFinanceCheckExecute string   `env:"JOB_FINANCE_CHECK_EXECUTE" default:"02:30:00"`
	EnableMock             bool     `env:"ENABLE_MOCK" default:"false"`
	MockAddress            []string `env:"MOCK_ADDRESS"`
}

type TelegramBotConfig struct {
	BotToken    string `env:"BOT_TOKEN" default:"7902615792:AAE1V_NQ0SNBdbBlnlebWp8TK8o9X80TFJE"`
	ChatId      string `env:"CHAT_ID" default:"-1002380058280"`
	TgEnabled   bool   `env:"TG_ENABLED" default:"false"`
	AdminUserId int    `env:"ADMIN_USER_ID" default:"7051939504"`
}

type S3Config struct {
	Region       string `env:"REGION" default:"us-east-1"`
	AccessKey    string `env:"ACCESS_KEY"`
	SecretKey    string `env:"SECRET_KEY"`
	Role         string `env:"ROLE"`
	BucketName   string `env:"BUCKET_NAME" default:"btfs-dev.bt.co"`
	StaticPrefix string `env:"STATIC_PREFIX" default:"guard_test/"`
}

type CsvConfig struct {
	CsvFilePath string `env:"CSV_FILE_PATH" default:"data"`
}

var (
	BttcConfig  = new(AirDropConfig)
	AwsS3Config = new(S3Config)
	FileConfig  = new(CsvConfig)
	TgBotConfig = new(TelegramBotConfig)
)

func init() {
	env.ScanTo(BttcConfig)
	env.ScanTo(AwsS3Config)
	env.ScanTo(FileConfig)
	env.ScanTo(TgBotConfig)
}
