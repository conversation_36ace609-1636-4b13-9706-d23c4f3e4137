default: run

# 1. open a shell, run the command
# ssh  -i ~/.ssh/id_rsa -L 54321:io-bt-nft-dev.cmnp6fvlnpft.ap-east-1.rds.amazonaws.com:5432 btfs@**************

# 2. import_csv
run:
	GO111MODULE=on \
	TGC_PREFIX=SS_ \
	SS_DB_URL="postgresql://postgres:nz8DKySUisi4y8ln5gP1@localhost:54321/db_scan_develop" \
	SS_DB_NUM_CONNS=300 SS_DB_STMT_TIMEOUT=30 GO111MODULE=on \
	\
	go run ../service/generate_merkle_tree/generate_merkle_tree.go  -epoch_day="2022-09-03"
