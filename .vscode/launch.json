{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Package",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "run_job"
            ],
            "env": {
                "TGC_PREFIX": "SS_",
                "SS_ENV": "dev",
                "SS_DB_URL": "postgresql://postgres:123456@localhost:5432/db_scan",
                "SS_DB_STMT_TIMEOUT": "30",
                "SS_DB_NUM_CONNS": "300",
                "JOB_AIRDROP_EXECUTE": "02:00:00"
            }
        },
        {
            "name": "finance check",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "env": {
                "TGC_PREFIX": "SS_",
                "SS_ENV": "dev",
                "SS_DB_URL": "postgresql://postgres:123456@localhost:5432/db_scan",
                "SS_DB_STMT_TIMEOUT": "30",
                "SS_DB_NUM_CONNS": "300",
                "JOB_AIRDROP_EXECUTE": "02:00:00"
            },
            "args": [
                "finance_check",
                "--period",
                "1",
                "--end",
                "2025-04-12"
            ],
        },
        {
            "name": "airdrop",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "env": {
                "TGC_PREFIX": "SS_",
                "SS_ENV": "dev",
                "SS_DB_URL": "postgresql://postgres:123456@localhost:5432/db_scan",
                "SS_DB_STMT_TIMEOUT": "30",
                "SS_DB_NUM_CONNS": "300",
                "JOB_AIRDROP_EXECUTE": "02:00:00"
            },
            "args": [
                "airdrop",
                "--date",
                "2025-04-01"
            ],
        }
    ]
}