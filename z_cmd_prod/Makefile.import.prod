default: run

# 1. open a shell, run the command
# ssh  -i ~/.ssh/id_rsa -L 53333:io-bt-nft-prod.cobiivawnqiz.ap-southeast-1.rds.amazonaws.com:5432 btfs@*************

# 2. import_csv
run:
	GO111MODULE=on \
	TGC_PREFIX=SS_ \
	SS_DB_URL="postgresql://postgres:XZQ1OiOHMLvxiaJKQkh1@localhost:53333/db_scan_production" \
	SS_DB_NUM_CONNS=300 SS_DB_STMT_TIMEOUT=30 GO111MODULE=on \
	\
	go run ../service/import_csv/import_csv.go -file_path "../data_source/input/test.result.csv" -epoch_day "2022-10-24"
