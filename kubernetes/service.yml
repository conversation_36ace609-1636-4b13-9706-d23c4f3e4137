apiVersion: apps/v1
kind: Deployment
metadata:
  name: $SERVICE_NAME
  namespace: $NAMESPACE
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: $SERVICE_NAME
  template:
    metadata:
      name: $SERVICE_NAME
      labels:
        app: $SERVICE_NAME
        version: v1
    spec:
      serviceAccountName: guard-s3
      containers:
        - name: $SERVICE_NAME
          image: $IMAGE_NAME
          imagePullPolicy: Always
          resources:
            requests:
              memory: "$MIN_MEM"
              cpu: "$MIN_CPU"
            limits:
              memory: "$MAX_MEM"
              cpu: "$MAX_CPU"
          envFrom:
            - configMapRef:
                name: $SERVICE_NAME-configmap
          tty: true
          stdin: true
          securityContext:
            privileged: true
      securityContext:
        fsGroup: 1000
