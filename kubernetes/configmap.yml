apiVersion: v1
kind: ConfigMap
metadata:
  name: $SERVICE_NAME-configmap
  namespace: $NAMESPACE
data:
  GIT_COMMIT_SHA: $CI_COMMIT_SHA
  GO111MODULE: "on"
  DB_HOST: "$DB_HOST"
  DB_PORT: "$DB_PORT"
  DB_USER: "$DB_USER"
  DB_PASSWORD: "$DB_PASSWORD"
  DB_NAME: "$DB_NAME"
  DB_MAX_OPEN_CONNS: "$DB_MAX_OPEN_CONNS"
  DB_MAX_CONN_LIFE_TIME: "$DB_MAX_CONN_LIFE_TIME"
  DB_MAX_CONN_IDLE_TIME: "$DB_MAX_CONN_IDLE_TIME"

  TGC_PREFIX: "SS_"
  SS_DB_URL: "postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"
  SS_DB_NUM_CONNS: '300'
  SS_DB_STMT_TIMEOUT: '30'

  CSV_FILE_PATH: "$CSV_FILE_PATH"

  BTTC_ADDRESS: "$BTTC_ADDRESS"
  CHAIN_ID: "$CHAIN_ID"
  PROPOSAL_PRIVATE_KEY: "$PROPOSAL_PRIVATE_KEY"
  REVIEW_PRIVATE_KEY: "$REVIEW_PRIVATE_KEY"
  PROXY_CONTRACT_ADDRESS: "$PROXY_CONTRACT_ADDRESS"
  CHECK_BATCH_PERCENT: "$CHECK_BATCH_PERCENT"
  FINANCE_CHECK_RANGE: "$FINANCE_CHECK_RANGE"
  BTTC_SCAN_API_URL: "$BTTC_SCAN_API_URL"
  BTTC_SCAN_API_KEY: "$BTTC_SCAN_API_KEY"
  JOB_AIRDROP_EXECUTE: "$JOB_AIRDROP_EXECUTE"
  JOB_FINANCE_CHECK_EXECUTE: "$JOB_FINANCE_CHECK_EXECUTE"

  REGION: "$REGION"
  ACCESS_KEY: "$ACCESS_KEY"
  SECRET_KEY: "$SECRET_KEY"
  ROLE: "$ROLE"
  BUCKET_NAME: "$BUCKET_NAME"
  STATIC_PREFIX: "$STATIC_PREFIX"

  TG_ENABLED: "$TG_ENABLED"
  BOT_TOKEN: "$BOT_TOKEN"
  CHAT_ID: "$CHAT_ID"
  ADMIN_USER_ID: "$ADMIN_USER_ID"

  ENABLE_MOCK: "$ENABLE_MOCK"
  MOCK_ADDRESS: "$MOCK_ADDRESS"
