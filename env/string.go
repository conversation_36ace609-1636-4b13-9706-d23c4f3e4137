package env

import (
	"fmt"
	"reflect"
	"strings"
)

type StringWrapper struct {
	c interface{}
}

func String(c interface{}) fmt.Stringer {
	return &StringWrapper{c}
}

func (w *StringWrapper) String() (str string) {
	builder := strings.Builder{}
	rsv := reflect.ValueOf(w.c)
	if rsv.Kind() == reflect.Ptr {
		if rsv.IsNil() {
			return "Nil"
		}
		rsv = rsv.Elem()
	}
	rst := rsv.Type()
	for i := 0; i < rsv.NumField(); i++ {
		rfs := rst.Field(i)
		rfv := rsv.Field(i)
		tag := rfs.Tag.Get(envTag)
		visible := "show"
		if tag != "" {
			parts := strings.Split(tag, ",")
			if len(parts) > 2 {
				visible = parts[2]
			}
		}
		switch visible {
		case "hide":
			builder.WriteString(fmt.Sprintf("<%s:******>", rfs.Name))
		default:
			builder.WriteString(fmt.Sprintf("<%s:%v>", rfs.Name, rfv.Interface()))
		}
	}

	str = builder.String()

	return
}
