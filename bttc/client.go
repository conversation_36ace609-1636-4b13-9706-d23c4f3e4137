package bttc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math/big"
	"net/http"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/ethereum/go-ethereum/rpc"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/common/airdrop"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/config"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/dal"
)

var (
	backend              *ethclient.Client
	proposalAuth         *bind.TransactOpts
	reviewAuth           *bind.TransactOpts
	airdropContractABI   abi.ABI
	proxyContractAddress common.Address
)

func init() {
	var err error
	backend, err = ethclient.Dial(config.BttcConfig.BttcAddress)
	if err != nil {
		log.Printf("Failed to connect to the Ethereum client: %v", err)
		return
	}

	proposalPrivateKey, err := crypto.HexToECDSA(config.BttcConfig.ProposalPrivateKey)
	if err != nil {
		log.Printf("Failed to parse proposal private key: %v", err)
		return
	}

	proposalAuth, err = bind.NewKeyedTransactorWithChainID(proposalPrivateKey, big.NewInt(config.BttcConfig.ChainID))
	if err != nil {
		log.Printf("Failed to create proposal transaction: %v", err)
		return
	}

	reviewPrivateKey, err := crypto.HexToECDSA(config.BttcConfig.ReviewPrivateKey)
	if err != nil {
		log.Printf("Failed to parse review private key: %v", err)
		return
	}

	reviewAuth, err = bind.NewKeyedTransactorWithChainID(reviewPrivateKey, big.NewInt(config.BttcConfig.ChainID))
	if err != nil {
		log.Printf("Failed to create review transaction: %v", err)
		return
	}

	airdropContractABI, err = abi.JSON(strings.NewReader(airdrop.AirdropABI))
	if err != nil {
		log.Printf("Failed to load airdrop contract: %v", err)
		return
	}

	proxyContractAddress = common.HexToAddress(config.BttcConfig.ProxyContractAddress)
}

func ProposalMerkleRoot(epochDay string) error {
	airdropRecord, err := dal.GetAirDrop(epochDay)
	if err != nil {
		log.Printf("Failed to get merkle root: %v", err)
		return err
	}
	privateKey, err := crypto.HexToECDSA(config.BttcConfig.ProposalPrivateKey)
	if err != nil {
		log.Printf("Failed to parse private key: %v", err)
		return err
	}

	fromAddress := crypto.PubkeyToAddress(privateKey.PublicKey)
	nonce, err := backend.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		log.Printf("Failed to get nonce: %v", err)
		return err
	}

	root := common.HexToHash(airdropRecord.MerkleRoot)
	tokenTotal := new(big.Int)
	tokenTotal.SetString(airdropRecord.TokenTotal+"000000000000000000", 10)

	callData, err := airdropContractABI.Pack("proposeMerkleRoot", epochDay, root, tokenTotal)
	if err != nil {
		log.Printf("Failed to pack propoal merkle root data: %v", err)
		return err
	}

	gasLimit := uint64(200000)
	gasPrice, err := backend.SuggestGasPrice(context.Background())
	if err != nil {
		log.Printf("Failed to get gas price: %v", err)
		return err
	}

	tx := types.NewTransaction(nonce, proxyContractAddress, big.NewInt(0), gasLimit, gasPrice, callData)

	signedTx, err := proposalAuth.Signer(fromAddress, tx)
	if err != nil {
		log.Printf("Failed to sign transaction: %v", err)
		return err
	}
	err = backend.SendTransaction(context.Background(), signedTx)
	if err != nil {
		log.Printf("Failed to send proposal-merkle-tree transaction: %v", err)
		return err
	}
	fmt.Printf("ProposeMerkleRoot OK, %s", signedTx.Hash().Hex())
	return nil
}

func ReviewPendingMerkleRoot() {
	privateKey, err := crypto.HexToECDSA(config.BttcConfig.ReviewPrivateKey)
	if err != nil {
		log.Printf("Failed to parse private key: %v", err)
		return
	}

	fromAddress := crypto.PubkeyToAddress(privateKey.PublicKey)
	nonce, err := backend.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		log.Printf("Failed to get nonce: %v", err)
		return
	}

	callData, err := airdropContractABI.Pack("reviewPendingMerkleRoot", true)
	if err != nil {
		log.Printf("Failed to pack review merkle root data: %v", err)
		return
	}

	gasLimit := uint64(200000)

	if err != nil {
		log.Printf("Failed to estimate gas: %v", err)
		return
	}

	gasPrice, err := backend.SuggestGasPrice(context.Background())
	if err != nil {
		log.Printf("Failed to get gas price: %v", err)
		return
	}

	tx := types.NewTransaction(nonce, proxyContractAddress, big.NewInt(0), gasLimit, gasPrice, callData)

	signedTx, err := reviewAuth.Signer(fromAddress, tx)
	if err != nil {
		log.Printf("Failed to sign transaction: %v", err)
		return
	}
	err = backend.SendTransaction(context.Background(), signedTx)
	if err != nil {
		log.Printf("Failed to send transaction: %v", err)
		return
	}

	fmt.Printf("ReviewMerkleRoot OK, %s", signedTx.Hash().Hex())
}

func SetClaimAvailable() {
	privateKey, err := crypto.HexToECDSA(config.BttcConfig.ReviewPrivateKey)
	if err != nil {
		log.Printf("Failed to parse private key: %v", err)
		return
	}
	fromAddress := crypto.PubkeyToAddress(privateKey.PublicKey)
	nonce, err := backend.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		log.Printf("Failed to get nonce: %v", err)
		return
	}
	callData, err := airdropContractABI.Pack("setClaimAvailable")
	if err != nil {
		log.Printf("Failed to pack review merkle root data: %v", err)
		return
	}
	gasLimit := uint64(200000)

	if err != nil {
		log.Printf("Failed to estimate gas: %v", err)
		return
	}

	gasPrice, err := backend.SuggestGasPrice(context.Background())
	if err != nil {
		log.Printf("Failed to get gas price: %v", err)
		return
	}

	tx := types.NewTransaction(nonce, proxyContractAddress, big.NewInt(0), gasLimit, gasPrice, callData)

	signedTx, err := reviewAuth.Signer(fromAddress, tx)
	if err != nil {
		log.Printf("Failed to sign transaction: %v", err)
		return
	}
	err = backend.SendTransaction(context.Background(), signedTx)
	if err != nil {
		log.Printf("Failed to send transaction: %v", err)
		return
	}

	fmt.Printf("SetClaimAvilable OK, %s", signedTx.Hash().Hex())

}

var (
	ReachedLimit = errors.New("reached limit")
)

func VerifyMerkleProof(merkleRoot, bttcAddress, amount, proof string) error {
	pf := make([]string, 0)
	err := json.Unmarshal([]byte(proof), &pf)
	if err != nil {
		return err
	}

	byte32Proof := make([]common.Hash, len(pf))
	for i, p := range pf {
		byte32Proof[i] = common.HexToHash(p)
	}

	at := new(big.Int)
	at.SetString(amount, 10)

	callData, err := airdropContractABI.Pack("merkleVerify", common.HexToHash(merkleRoot), at, byte32Proof, common.HexToAddress(bttcAddress))
	if err != nil {
		log.Printf("Failed to pack propoal merkle root data: %v", err)
	}

	callMsg := ethereum.CallMsg{
		To:   &proxyContractAddress,
		Data: callData,
	}

	res, err := backend.CallContract(context.Background(), callMsg, nil)
	if err != nil {
		var e rpc.HTTPError
		if errors.As(err, &e) && e.StatusCode == http.StatusServiceUnavailable {
			return ReachedLimit
		}
		return nil
	}
	result, err := airdropContractABI.Unpack("merkleVerify", res)
	if err != nil {
		log.Printf("Failed to unpack result: %v", err)
		return err
	}
	success := result[0].(bool)
	balance := result[1].(*big.Int)
	if !(success && balance.Cmp(at) == 0) {
		fmt.Printf("verify merkle proof failed, bttcAddress: %s, amount: %s\n", bttcAddress, amount)
		return fmt.Errorf("verify merkle proof failed, bttcAddress: %s, amount: %s", bttcAddress, amount)
	}
	return nil
}
