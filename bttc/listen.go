package bttc

import (
	"math/big"
	"strconv"

	"github.com/ethereum/go-ethereum/ethclient"
	log "github.com/sirupsen/logrus"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/config"
)

func StartBttcListener(from, to string) (*big.Int, *big.Int, error) {
	log.Infof("Run bttc listener job..., from: %s, to: %s", from, to)

	cli, err := ethclient.Dial(config.BttcConfig.BttcAddress)
	if err != nil {
		log.Errorf("init backend with end point:%+v,err:%+v", config.BttcConfig.BttcAddress, err)
		return new(big.Int), new(big.Int), err
	}
	log.Infof("init backend with end point:%+v", config.BttcConfig.BttcAddress)

	lis := NewBttcListener(cli, nil)

	f, err := strconv.ParseUint(from, 10, 64)
	if err != nil {
		return new(big.Int), new(big.Int), err
	}
	t, err := strconv.ParseUint(to, 10, 64)
	if err != nil {
		return new(big.Int), new(big.Int), err
	}

	amount, claimedAmount, err := lis.Listen(f+1, t)
	log.Infof("StartBttcListener job ends")
	return amount, claimedAmount, err
}

func StartMonitor(start uint64) (*big.Int, uint64, error) {
	log.Info("Run monitor job...")
	cli, err := ethclient.Dial(config.BttcConfig.BttcAddress)
	if err != nil {
		log.Errorf("init backend with end point:%+v,err:%+v", config.BttcConfig.BttcAddress, err)
		return new(big.Int), start, nil
	}
	log.Infof("init backend with end point:%+v", config.BttcConfig.BttcAddress)

	lis := NewBttcListener(cli, nil)

	return lis.MonitorTransfer(start)
}
