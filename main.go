package main

import (
	"context"
	"fmt"
	"os"
	"time"

	log "github.com/sirupsen/logrus"
	"github.com/urfave/cli/v2"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/job"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/service"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/service/generate_merkle_tree"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/service/import_csv"
	util "gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/utils"
)

var cmds = &cli.App{
	Name:    "airdrop",
	Version: "0.0.1",
	Usage:   "get csv from s3 and generate merkle tree for airdrop",
	Commands: []*cli.Command{
		{
			Name: "import_csv",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:  "file_path",
					Value: "./data",
				},
				&cli.StringFlag{
					Name:  "epoch_day",
					Value: time.Now().Format("2006-01-02"),
				},
			},
			Action: func(ctx *cli.Context) error {
				filePath := ctx.String("file_path")
				epochDay := ctx.String("epoch_day")
				log.Println("filePath:", filePath, "epochDay:", epochDay)
				import_csv.ImportCsv(filePath, epochDay)
				return nil
			},
		},
		{
			Name: "generate_merkle_tree",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:  "epoch_day",
					Value: time.Now().Format("2006-01-02"),
				},
			},
			Action: func(ctx *cli.Context) (err error) {
				epochDay := ctx.String("epoch_day")
				generate_merkle_tree.GenerateMerkleTree(epochDay)
				return nil
			},
		},
		{
			Name: "merkle_proof_check",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:  "epoch_day",
					Value: time.Now().Format("2006-01-02"),
				},
			},
			Action: func(ctx *cli.Context) (err error) {
				epochDay := ctx.String("epoch_day")
				service.PreCheckMerkleProof(epochDay)
				return nil
			},
		},
		{
			Name: "finance_check",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:  "period",
					Value: "7",
				},
				&cli.StringFlag{
					Name:  "end",
					Value: time.Now().AddDate(0, 0, -1).Format("2006-01-02"),
				},
			},
			Action: func(ctx *cli.Context) error {
				period := ctx.Int("period")
				endDay := ctx.String("end")
				end, _ := time.Parse("2006-01-02", endDay)
				service.FinanceCheck(period, end)
				return nil
			},
		},
		{
			Name: "airdrop",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:  "date",
					Value: time.Now().AddDate(0, 0, -1).Format("2006-01-02"),
				},
			},
			Action: func(ctx *cli.Context) error {
				date := ctx.String("date")
				d, _ := time.Parse("2006-01-02", date)
				job.AirDropJob(d)
				return nil
			},
		},
		{
			Name: "run_job",
			Action: func(ctx *cli.Context) (err error) {
				job.RunAirdrop()
				return nil
			},
		},
	},
}

func main() {

	// start bot to execute some target command.
	go util.NewBot(job.AirDropJob)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err := cmds.RunContext(ctx, os.Args)
	if err != nil {
		_, _ = fmt.Fprint(os.Stderr, err)
	}
}
