package postgres

import (
	tgpg "github.com/tron-us/go-common/v2/db/postgres"
	dbenv "github.com/tron-us/go-common/v2/env/db"
)

var (
	PGDBRead  *tgpg.TGPGDB // concurrent access ok
	PGDBWrite *tgpg.TGPGDB
)

func init() {

	PGDBWrite = tgpg.CreateTGPGDBWithOptions(&tgpg.TGPGDBOptions{Url: dbenv.DBWriteURL, DisableBeforeQueryLog: true, DisableAfterQueryLog: true})
	PGDBRead = tgpg.CreateTGPGDBWithOptions(&tgpg.TGPGDBOptions{Url: dbenv.DBWriteURL, DisableBeforeQueryLog: true, DisableAfterQueryLog: true})
	// Keep DB connected as long as process is alive
}
