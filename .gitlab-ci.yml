include:
  - project: 'devops/gitlab'
    file: 'golang.gitlab-ci.yml'

.common.deploy:
  after_script:
    - export NAMESPACE="$NAMESPACE-$CI_ENVIRONMENT_NAME"
    - aws eks --region $EKS_REGION update-kubeconfig --name $EKS_NAME
    - kubectl rollout status deployments $SERVICE_NAME -n $NAMESPACE

.common.build:
  script:
    - docker build  --network host --build-arg GITLAB_TOKEN=$GITLAB_TOKEN -t $IMAGE_NAME .
    - docker push $IMAGE_NAME


go:
  script:
    - go fmt
    - go mod tidy
