package util

import (
	"bufio"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/config"
)

func SendTelegramMessage(message string) {
	if !config.TgBotConfig.TgEnabled {
		return
	}
	apiURL := fmt.Sprintf("https://api.telegram.org/bot%s/sendMessage", config.TgBotConfig.BotToken)

	params := url.Values{}
	params.Add("chat_id", config.TgBotConfig.ChatId)
	params.Add("text", message)

	resp, err := http.Get(apiURL + "?" + params.Encode())
	if err != nil {
		fmt.Printf("error sending message: %v", err)
		return
	}
	defer resp.Body.Close()
}

func SendFile(filePath string) error {
	if !config.TgBotConfig.TgEnabled {
		return nil
	}

	bot, err := tgbotapi.NewBotAPI(config.TgBotConfig.BotToken)
	if err != nil {
		return fmt.Errorf("创建 Telegram 机器人失败: %w", err)
	}

	chatID, err := strconv.ParseInt(config.TgBotConfig.ChatId, 10, 64)
	if err != nil {
		return fmt.Errorf("解析聊天ID失败: %w", err)
	}

	// 文件过大，需要分片发送
	parts, err := splitCSVFile(filePath)
	if err != nil {
		return fmt.Errorf("分片文件失败: %w", err)
	}

	// 发送分片文件
	for i, part := range parts {
		file := tgbotapi.NewDocument(chatID, tgbotapi.FilePath(part))
		file.Caption = fmt.Sprintf("📎 对账CSV文件 (部分 %d/%d)", i+1, len(parts))

		if _, err := bot.Send(file); err != nil {
			return fmt.Errorf("发送分片文件失败: %w", err)
		}

		// 删除临时分片文件
		os.Remove(part)
	}

	fmt.Printf("CSV 文件已分 %d 个部分发送至 Telegram!\n", len(parts))
	return nil
}

const maxFileSize = 50 * 1024 * 1024 // Telegram 最大支持 50MB

func splitCSVFile(filePath string) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	baseDir := filepath.Dir(filePath)
	baseName := filepath.Base(filePath)
	ext := filepath.Ext(baseName)
	nameWithoutExt := strings.TrimSuffix(baseName, ext)

	var parts []string
	partNum := 1
	scanner := bufio.NewScanner(file)

	currentPart, err := os.Create(filepath.Join(baseDir, fmt.Sprintf("%s_part%d%s", nameWithoutExt, partNum, ext)))
	if err != nil {
		return nil, fmt.Errorf("创建分片文件失败: %w", err)
	}
	parts = append(parts, currentPart.Name())

	currentSize := 0
	for scanner.Scan() {
		line := scanner.Text() + "\n"
		lineSize := len(line)

		if currentSize+lineSize > maxFileSize {
			currentPart.Close()
			partNum++
			currentPart, err = os.Create(filepath.Join(baseDir, fmt.Sprintf("%s_part%d%s", nameWithoutExt, partNum, ext)))
			if err != nil {
				return nil, fmt.Errorf("创建分片文件失败: %w", err)
			}
			parts = append(parts, currentPart.Name())
			currentSize = 0
		}

		currentPart.WriteString(line)
		currentSize += lineSize
	}
	currentPart.Close()

	return parts, nil
}

var lastTgMsgId int

func init() {
	bot, err := tgbotapi.NewBotAPI(config.TgBotConfig.BotToken)
	if err != nil {
		log.Printf("创建 Telegram 机器人失败: %v", err)
		panic(err)
	}

	bot.Debug = true // 开启调试日志

	u := tgbotapi.NewUpdate(0)
	u.Timeout = 60

	updates, err := bot.GetUpdates(u)
	if err != nil {
		log.Printf("获取更新失败: %v", err)
		panic(err)
	}

	for _, update := range updates {
		if update.Message != nil {
			lastTgMsgId = update.Message.MessageID
		}
	}

	log.Printf("Last tg msg id: %d", lastTgMsgId)
}

func NewBot(executeFunc func(time.Time)) {
	defer func() {
		if err := recover(); err != nil {
			log.Printf("Bot panic: %v", err)
		}
	}()

	bot, err := tgbotapi.NewBotAPI(config.TgBotConfig.BotToken)
	if err != nil {
		log.Panic(err)
	}

	bot.Debug = false // 开启调试日志

	u := tgbotapi.NewUpdate(lastTgMsgId + 1)
	u.Timeout = 60

	updates := bot.GetUpdatesChan(u)

	for update := range updates {
		if update.Message == nil {
			continue
		}

		if update.Message.Chat.IsGroup() || update.Message.Chat.IsSuperGroup() {
			text := update.Message.Text
			if strings.Contains(text, "@"+bot.Self.UserName) {
				log.Printf("收到指令：%s", text)
				// 只接受指定人@发送的消息
				if update.Message.From.ID != int64(config.TgBotConfig.AdminUserId) {
					continue
				}

				// 提取指令  airdrop --date 2025-03-25
				parts := strings.Fields(text)
				if len(parts) < 3 {
					continue
				}

				cmd := parts[1]
				// 提取参数
				arg := parts[2]
				if len(parts) < 3 {
					msg := tgbotapi.NewMessage(update.Message.Chat.ID, "参数格式错误，正确格式: airdrop --date 2025-03-25")
					bot.Send(msg)
					continue
				}
				// 解析参数
				params := parts[3]

				// 命令校验
				var airdropDate time.Time
				if cmd == "airdrop" && arg == "--date" {
					airdropDate, err = time.Parse("2006-01-02", params)
					if err != nil {
						log.Printf("日期格式错误: %s", params)
						msg := tgbotapi.NewMessage(update.Message.Chat.ID, "日期格式错误，请使用 YYYY-MM-DD 格式，例如: 2025-03-25")
						bot.Send(msg)
						continue
					}
				}

				// 执行命令
				log.Println("开始执行收到的指令", airdropDate)
				executeFunc(airdropDate)
			}
		}
	}
}
