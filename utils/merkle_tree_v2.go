package util

import (
	"fmt"
	"time"

	smt "github.com/FantasyJony/openzeppelin-merkle-tree-go/standard_merkle_tree"
	"github.com/ethereum/go-ethereum/common/hexutil"
)

type SumOrigin struct {
	Amount      int64  `json:"amount"`
	BttcAddress string `json:"bttc_address"`
}

type Origin struct {
	tableName    string    `pg:"origin,alias:t,discard_unknown_columns"`
	ID           uint64    `json:"id" pg:"id"`
	NodeId       string    `json:"node_id" pg:"node_id"`
	TronAddr     string    `json:"tron_addr" pg:"tron_addr"`
	BttcAddr     string    `json:"bttc_addr" pg:"bttc_addr"`
	EpochDay     string    `json:"epoch_day" pg:"epoch_day"`
	Epoch        int       `json:"epoch" pg:"epoch"`
	AmountBttOld int64     `json:"amount_btt_old" pg:"amount_btt_old"`
	Amount       string    `json:"amount" pg:"amount"`
	ClaimFlag    bool      `json:"claim_flag" pg:"claim_flag"`
	CreatedTime  time.Time `json:"created_time" pg:"created_time"`
	UpdatedTime  time.Time `json:"updated_time" pg:"updated_time"`
}

func NewMerkleTree(origins []*Origin) (*smt.StandardTree, error) {
	values := make([][]interface{}, 0)
	for _, v := range origins {
		values = append(values, []interface{}{
			smt.SolAddress(v.BttcAddr),
			smt.SolNumber(v.Amount),
		})
	}

	leafEncodings := []string{
		smt.SOL_ADDRESS,
		smt.SOL_UINT256,
	}

	t1, err := smt.Of(values, leafEncodings)
	if err != nil {
		panic(err)
	}

	fmt.Println("Root: ", hexutil.Encode(t1.GetRoot()))
	return t1, err
}

func GetMerklePath(tree *smt.StandardTree, leaf []interface{}) ([]string, error) {
	leafs, err := tree.GetProof(leaf)
	if err != nil {
		panic(err)
	}

	proof := make([]string, len(leafs))
	for a, b := range leafs {
		proof[a] = hexutil.Encode(b)
	}

	return proof, err
}
