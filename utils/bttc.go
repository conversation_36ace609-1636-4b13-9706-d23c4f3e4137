package util

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math/big"
	"net/http"
	"time"

	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/config"
)

// P46IDMV5UF3HGX3FEJPD2RK5VFE1PM865Z
const (
	blockNumUrl = "%s?chainid=199&module=block&action=getblocknobytime&timestamp=%d&closest=before&apikey=%s"
	balanceUrl  = "%s?chainid=199&module=account&action=balancehistory&address=%s&blockno=%s&apikey=%s"
)

type Response struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Result  string `json:"result"`
}

func doRequest(url string) (*Response, error) {
	var (
		resp *http.Response
		err  error
	)

	result := &Response{}

	for i := 0; i < 100; i++ {
		resp, err = http.Get(url)
		if err != nil {
			log.Printf("请求失败: %v", err)
			time.Sleep(time.Duration(i+1) * time.Second) // 指数退避
			continue
		}

		defer resp.Body.Close()

		if resp == nil {
			return nil, fmt.Errorf("empty response")
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Printf("读取响应失败: %v", err)
			return nil, err
		}

		if err := json.Unmarshal(body, result); err != nil {
			log.Printf("解析响应失败: %v", err)
			continue
		}

		if result.Status != "1" {
			log.Printf("请求失败: %s", result.Result)
			continue
		}

		return result, nil
	}

	return nil, fmt.Errorf("达到最大重试次数")
}

func GetAirdropContractBalance(timestamp int64) (*big.Int, string, error) {
	// 获取区块号
	blockNumUrl := fmt.Sprintf(blockNumUrl, config.BttcConfig.BTTCScanApiUrl, timestamp, config.BttcConfig.BTTCScanApiKey)
	blockNumRes, err := doRequest(blockNumUrl)
	if err != nil {
		return nil, "", fmt.Errorf("获取区块号失败: %v", err)
	}

	// 获取余额
	balanceUrl := fmt.Sprintf(balanceUrl, config.BttcConfig.BTTCScanApiUrl,
		config.BttcConfig.ProxyContractAddress, blockNumRes.Result, config.BttcConfig.BTTCScanApiKey)
	balanceRes, err := doRequest(balanceUrl)
	if err != nil {
		return nil, "", fmt.Errorf("获取余额失败: %v", err)
	}

	balance := new(big.Int)
	if _, ok := balance.SetString(balanceRes.Result, 10); !ok {
		return nil, "", fmt.Errorf("解析余额失败: %s", balanceRes.Result)
	}

	return balance, blockNumRes.Result, nil
}
