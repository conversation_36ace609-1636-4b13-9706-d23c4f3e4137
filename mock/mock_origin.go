package mock

import (
	"fmt"
	"math/rand"
	"strconv"
	"time"

	log "github.com/sirupsen/logrus"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/dal"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/data"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/model"
)

const TotalAmount = 75 * 100 * 1000 * 1000

func DistributeToMockUsers(day string, epoch int) error {
	defer func() {
		if err := recover(); err != nil {
			log.Error("DistributeToMockUsers", "err", err)
		}
	}()

	// 1. 获取剩余金额
	remainingAmount, err := getRemainingBalance(epoch)
	if err != nil {
		return err
	}
	// 2. 将剩余金额随机分配给用户
	return distributeRemainingBalance(day, epoch, remainingAmount)

}

// 获取剩余金额
func getRemainingBalance(epoch int) (uint64, error) {
	totalAmount, err := dal.GetTotalAmountByEpoch(epoch)
	if err != nil {
		return 0, err
	}
	return TotalAmount - totalAmount, nil
}

// 将剩余金额随机分配给用户
func distributeRemainingBalance(day string, epoch int, remainAmount uint64) error {

	minAmount := uint64(1)
	maxAmount := uint64(10)

	addresses := data.MockAddress
	if len(addresses) == 0 {
		fmt.Println("mock address is empty skipped.")
		return nil
	}

	data := make([]*model.Origin, 0)
	for _, address := range addresses {
		amount := minAmount + rand.Uint64()%(maxAmount-minAmount+1)
		if amount > remainAmount {
			amount = remainAmount
		}

		remainAmount -= amount

		r := &model.Origin{
			NodeId:       generateRandomNodeId(),
			TronAddr:     generateRandomTronAddr(),
			BttcAddr:     address,
			EpochDay:     day,
			Epoch:        epoch,
			AmountBttOld: int64(amount),
			Amount:       strconv.Itoa(int(amount)) + "000000000000000000",
			CreatedTime:  time.Now(),
		}

		data = append(data, r)

		if remainAmount <= 0 {
			break
		}
	}

	return InsertMockData(data)
}

func InsertMockData(data []*model.Origin) error {
	return dal.InsertOrigin(data)
}

// 生成随机 NodeId
func generateRandomNodeId() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 52
	nodeId := make([]byte, length)
	for i := range nodeId {
		nodeId[i] = charset[rand.Intn(len(charset))]
	}
	return "16Uiu2HAm6k4a" + string(nodeId)
}

// 生成随机 TronAddr
func generateRandomTronAddr() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 34
	tronAddr := make([]byte, length)
	for i := range tronAddr {
		tronAddr[i] = charset[rand.Intn(len(charset))]
	}
	return string(tronAddr)
}
