package logic

import (
	"errors"
	"fmt"
	"math/big"
	"strconv"

	log "github.com/sirupsen/logrus"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/dal"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/model"
	util "gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/utils"
)

func Process(epochDay string) (int, error) {
	epoch, existCount, err := dal.IsExistAirdrop(epochDay)
	if err != nil {
		log.Error("insertAirdropInfo IsExistAirdrop, err:", err)
		return 0, err
	}

	if existCount > 0 {
		return -1, errors.New("epoch_day:" + epochDay + " is already exist! \n\n\n")
	}

	totalAmount, err := getTotalAmount(err, epoch)
	if err != nil {
		return 0, err
	}

	mpOrigin, err := dal.GetMapCurAmount(epoch)
	if err != nil {
		log.Error("AirdropProcess PrepareAirdrop, err:", err)
		return 0, err
	}

	var merkleContentList []util.MerkleContent
	for _, origin := range mpOrigin {
		am := model.AirdropMerkle{
			Origin: origin,
		}
		merkleContentList = append(merkleContentList, &am)
	}

	merkleTree, err := GenerateMerkleTreeV2(merkleContentList, epochDay)
	// for _, n := range merkleTree.Leafs {
	// 	fmt.Println(n.C.BttcAddress(), "left: ", hexutil.Encode(n.Left.Hash), "right: ", hexutil.Encode(n.Right.Hash))
	// }
	for _, origin := range merkleContentList {
		ok, err := merkleTree.VerifyContent(origin)
		fmt.Println(ok, err)
	}

	if err != nil {
		log.Error("AirdropProcess mpRow, err:", err)
		return 0, err
	}

	// 2.insert into merkle list
	err = InsertMerkleTree(epoch, epochDay, totalAmount.String(), mpOrigin, merkleTree, merkleContentList)
	if err != nil {
		log.Errorf("insertMerkleTree err = %s", err)
		return 0, err
	}
	log.Info("MainProcess OK! merkleRoot = ", merkleTree.MerkleRoot())

	return 0, nil
}

func getTotalAmountMap(epoch int, allAddress []string) (map[string]*util.SumOrigin, error) {
	l, err := dal.GetMapListV2WithPage(epoch, allAddress)
	if err != nil {
		return nil, err
	}
	res := make(map[string]*util.SumOrigin)
	for _, v := range l {
		res[v.BttcAddr] = &util.SumOrigin{
			Amount:      v.AmountBttOld,
			BttcAddress: v.BttcAddr,
		}
	}

	return res, nil
}

func getTotalAmount(err error, epoch int) (*big.Int, error) {
	originList, err := dal.GetMapListV2(epoch)
	if err != nil {
		log.Error("AirdropProcess PrepareAirdrop, err:", err)
		return nil, err
	}

	totalAmount := new(big.Int)
	for _, o := range originList {
		totalAmount = totalAmount.Add(totalAmount, big.NewInt(o.AmountBttOld))
	}
	return totalAmount, nil
}

func getTotalAmountV2(epoch int) (*big.Int, error) {
	originList, err := dal.GetTotalAmountWithPage(epoch)
	if err != nil {
		log.Error("AirdropProcess PrepareAirdrop, err:", err)
		return nil, err
	}

	totalAmount := new(big.Int)
	for _, o := range originList {
		totalAmount = totalAmount.Add(totalAmount, big.NewInt(o.AmountBttOld))
	}
	return totalAmount, nil
}

func ProcessV2(epochDay string) (int, error) {
	epoch, existCount, err := dal.IsExistAirdrop(epochDay)
	if err != nil {
		log.Error("insertAirdropInfo IsExistAirdrop, err:", err)
		return 0, err
	}

	if existCount > 0 {
		return -1, errors.New("epoch_day:" + epochDay + " is already exist! \n\n\n")
	}

	// 1. 获取 origin epoch 全量数据
	originList, err := dal.OriginListPaginate(epoch)
	if err != nil {
		log.Error("AirdropProcess PrepareAirdrop, err:", err)
		return 0, err
	}
	// 2. 计算 totalAmount
	totalAmount := new(big.Int)
	for _, o := range originList {
		totalAmount = totalAmount.Add(totalAmount, big.NewInt(o.AmountBttOld))
	}

	mpOrigin := make(map[string]*model.Origin)
	for i, v := range originList {
		mpOrigin[v.BttcAddr] = originList[i]
	}

	// 3. 获取 origin 累计的 amount
	allAddress := make([]string, 0)
	for _, v := range originList {
		allAddress = append(allAddress, v.BttcAddr)
	}
	aMap, err := getTotalAmountMap(epoch, allAddress)
	if err != nil {
		log.Error("AirdropProcess PrepareAirdrop, err:", err)
		return 0, err
	}

	var merkleContentList []util.MerkleContent
	for _, origin := range aMap {
		am := model.AirdropMerkle{
			Origin: &model.Origin{
				BttcAddr:     origin.BttcAddress,
				AmountBttOld: origin.Amount,
			},
		}
		merkleContentList = append(merkleContentList, &am)
	}

	for i, origin := range mpOrigin {
		mpOrigin[i].AmountBttOld = aMap[origin.BttcAddr].Amount
		mpOrigin[i].Amount = strconv.Itoa(int(aMap[origin.BttcAddr].Amount)) + priceRateStr
	}

	nodeList := make([]*util.Origin, 0)
	for _, origin := range mpOrigin {
		am := &util.Origin{
			ID:           origin.ID,
			NodeId:       origin.NodeId,
			TronAddr:     origin.TronAddr,
			BttcAddr:     origin.BttcAddr,
			EpochDay:     origin.EpochDay,
			Epoch:        origin.Epoch,
			AmountBttOld: aMap[origin.BttcAddr].Amount,
			Amount:       strconv.Itoa(int(aMap[origin.BttcAddr].Amount)) + priceRateStr,
			ClaimFlag:    origin.ClaimFlag,
			CreatedTime:  origin.CreatedTime,
			UpdatedTime:  origin.UpdatedTime,
		}
		nodeList = append(nodeList, am)
	}

	merkleTree, err := GenerateMerkleTreeV3(nodeList)

	if err != nil {
		log.Error("AirdropProcess mpRow, err:", err)
		return 0, err
	}

	// 2.insert into merkle list
	err = InsertMerkleTreeV3(epoch, epochDay, totalAmount.String(), mpOrigin, merkleTree)
	if err != nil {
		log.Errorf("insertMerkleTree err = %s", err)
		return 0, err
	}
	log.Info("MainProcess OK! merkleRoot = ", merkleTree.GetRoot())

	return 0, nil
}
