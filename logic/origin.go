package logic

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"
	"time"

	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/common"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/dal"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/model"

	log "github.com/sirupsen/logrus"
)

const total = 75 * 100 * 1000 * 1000

func ReadAirdropCsv(filePath string) (map[string]float32, map[string]interface{}, error) {

	totalWeight, err := getTotalWeight(filePath)
	if err != nil {
		return nil, nil, err
	}

	fs, err := os.Open(filePath)
	if err != nil {
		log.Printf("can not open the file, err is %+v", err)
		return nil, nil, err
	}
	defer fs.Close()

	mpBalances := make(map[string]float32)
	mpRow := make(map[string]interface{})
	r := csv.NewReader(fs)
	_, err = r.Read()
	if err != nil {
		log.Error("can not read, err is %+v", err)
		return nil, nil, err
	}

	am := 0
	for {
		row, err := r.Read()
		if err != nil && err != io.EOF {
			log.Error("can not read, err is %+v", err)
			return nil, nil, err
		}
		if err == io.EOF {
			break
		}

		// tronAddr := strings.TrimSpace(row[4])
		// amountStr := strings.TrimSpace(row[5])
		ethAddrStr := strings.TrimSpace(row[10])
		weight := strings.TrimSpace(row[2])

		if len(ethAddrStr) > 0 && len(weight) > 0 {
			w, err := strconv.Atoi(weight)
			if err != nil {
				log.Error("strconv.ParseFloat(amountStr, 64), err is %+v", err)
				return nil, nil, err
			}
			p := float32(float32(w)*10000.0/float32(totalWeight)) / 100.0
			amount := p * total / 100
			am += int(amount)
			ethAddr := ethAddrStr[2:]
			amountTmp, ok := mpBalances[ethAddr]
			if ok {
				mpBalances[ethAddr] = amountTmp + amount
			} else {
				mpBalances[ethAddr] = amount
			}

			mpRow[ethAddr] = row
		}
	}

	return mpBalances, mpRow, nil
}

func InsertOriginProcess(filePath, epochDay string) (int, error) {
	existCount, err := dal.IsExistOrigin(epochDay)
	if err != nil {
		log.Error("insertAirdropInfo IsExistOrigin, err:", err)
		return 0, err
	}

	if existCount > 0 {
		return -1, errors.New("epoch_day:" + epochDay + " is already exist! \n\n\n")
	}

	mpBalances, mpRow, err := ReadAirdropCsv(filePath)
	if err != nil {
		log.Error("ReadAirdropCsv() failed with %s", err)
		return 0, err
	}
	now := time.Now()

	epoch, err := common.GetEpochValue(epochDay)
	if err != nil {
		return 0, err
	}

	index := 0
	rows := make([]*model.Origin, 0)
	for ethAddr, rowInterface := range mpRow {
		row := rowInterface.([]string)
		if len(row) > 0 {
			index++
			nodeId := strings.TrimSpace(row[0])
			tronAddr := strings.TrimSpace(row[4])
			amountBttOld := mpBalances[ethAddr]

			r := &model.Origin{
				NodeId:       nodeId,
				TronAddr:     tronAddr,
				BttcAddr:     ethAddr,
				EpochDay:     epochDay,
				Epoch:        epoch,
				AmountBttOld: int64(amountBttOld),
				Amount:       strconv.Itoa(int(amountBttOld)) + priceRateStr,
				CreatedTime:  now,
			}
			rows = append(rows, r)
			if index%10000 == 0 {
				err = dal.InsertOrigin(rows)
				if err != nil {
					log.Error("insertAirdropInfo InsertOrigin, err:", err)
					return 0, err
				}
				rows = make([]*model.Origin, 0)
			}
		}
	}
	if len(rows) > 0 {
		err = dal.InsertOrigin(rows)
		if err != nil {
			log.Error("insertAirdropInfo InsertOrigin, err:", err)
			return 0, err
		}
	}

	fmt.Println("insertAirdropInfo OK ")
	return epoch, nil
}

func getTotalWeight(filePath string) (int, error) {
	fs, err := os.Open(filePath)
	if err != nil {
		log.Printf("can not open the file, err is %+v", err)
		return 0, err
	}
	defer fs.Close()

	r := csv.NewReader(fs)
	_, err = r.Read()
	if err != nil {
		log.Error("can not read, err is %+v", err)
		return 0, err
	}

	totalWeight := 0

	for {
		row, err := r.Read()
		if err != nil && err != io.EOF {
			log.Error("can not read, err is %+v", err)
			return 0, err
		}
		if err == io.EOF {
			break
		}

		weight := strings.TrimSpace(row[2])
		if len(weight) > 0 {
			intValue, err := strconv.Atoi(weight)
			if err != nil {
				log.Error("strconv.ParseFloat(weight, 64), err is %+v", err)
				return 0, err
			}
			totalWeight += intValue
		}
	}
	return totalWeight, nil
}
