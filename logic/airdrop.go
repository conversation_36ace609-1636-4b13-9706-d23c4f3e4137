package logic

import (
	"encoding/json"
	"math/big"
	"math/rand"
	"os/exec"
	"strconv"
	"time"

	smt "github.com/FantasyJony/openzeppelin-merkle-tree-go/standard_merkle_tree"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/common"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/dal"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/model"
	util "gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/utils"
	"golang.org/x/crypto/sha3"

	"github.com/ethereum/go-ethereum/common/hexutil"
	log "github.com/sirupsen/logrus"
)

func insertAirdrop(epoch int, epochDay string, tokenTotal string, merkleTree *util.MerkleTree) error {
	// tokenTotal := new(big.Int)
	// _, bl := tokenTotal.SetString(merkleTree.TokenTotal[2:], 16)
	// if !bl {
	// 	return errors.New("i.SetString false, when TokenTotal = " + merkleTree.TokenTotal)
	// }

	rows := &model.Airdrop{
		Epoch:       epoch,
		EpochDay:    epochDay,
		MerkleRoot:  hexutil.Encode(merkleTree.MerkleRoot()),
		TokenTotal:  tokenTotal,
		CreatedTime: time.Now(),
	}
	err := dal.InsertAirdropTab(rows)
	if err != nil {
		log.Errorf("InsertEpoch err = %v", err)
		return err
	}

	return nil
}

func insertAirdropV2(epoch int, epochDay string, tokenTotal string, merkleTree *smt.StandardTree) error {
	// tokenTotal := new(big.Int)
	// _, bl := tokenTotal.SetString(merkleTree.TokenTotal[2:], 16)
	// if !bl {
	// 	return errors.New("i.SetString false, when TokenTotal = " + merkleTree.TokenTotal)
	// }

	rows := &model.Airdrop{
		Epoch:       epoch,
		EpochDay:    epochDay,
		MerkleRoot:  hexutil.Encode(merkleTree.GetRoot()),
		TokenTotal:  tokenTotal,
		CreatedTime: time.Now(),
	}
	err := dal.InsertAirdropTab(rows)
	if err != nil {
		log.Errorf("InsertEpoch err = %v", err)
		return err
	}

	return nil
}

type MerkleTree struct {
	MerkleRoot string                 `json:"merkleRoot"`
	TokenTotal string                 `json:"tokenTotal"`
	Claims     map[string]NodeIdProof `json:"claims"`
}

type NodeIdProof struct {
	Index  int      `json:"index"`
	Amount string   `json:"amount"`
	Proof  []string `json:"proof"`
}

func insertAirdropStats(as *model.AirdropStats) error {
	err := dal.InsertAirdropStatsTab(as)
	if err != nil {
		log.Error("insertAirdropInfo InsertAirdrop, err:", err)
		return err
	}

	return nil
}

func batchInsertAirdropStats(as []*model.AirdropStats) error {
	err := dal.BatchInsertAirdropStatsTab(as)
	if err != nil {
		log.Error("insertAirdropInfo InsertAirdrop, err:", err)
		return err
	}

	return nil
}

func InsertMerkleTree(epoch int, epochDay, tokenTotal string, mpOrigin map[string]*model.Origin, merkleTree *util.MerkleTree, merkleNodes []util.MerkleContent) error {
	log.Info("insertAirdropTab begin... ", common.NowTime())
	if err := insertAirdrop(epoch, epochDay, tokenTotal, merkleTree); err != nil {
		log.Errorf("insertAirdrop err = %v", err)
		return err
	}
	log.Info("insertAirdropTab end... ", common.NowTime())

	log.Info("insertMerkleTreeList begin... ", common.NowTime())

	for _, v := range merkleNodes {
		paths, err := merkleTree.GetMerklePath(v)
		if err != nil {
			log.Error("insertAirdropInfo GetMerklePath, err:", err)
			return err
		}
		var proof []string
		for _, path := range paths {
			proof = append(proof, hexutil.Encode(path))
		}

		proofStr, err := json.Marshal(proof)
		if err != nil {
			log.Error("insertAirdropInfo json.Marshal, err:", err)
			return err
		}

		curAmount := new(big.Int).Mul(big.NewInt(int64(mpOrigin[v.BttcAddress()].AmountBttOld)), big.NewInt(priceRate))

		airdropStats := &model.AirdropStats{
			Epoch:          epoch,
			EpochDay:       epochDay,
			Level:          1,
			NodeId:         mpOrigin[v.BttcAddress()].NodeId,
			BttcAddr:       v.BttcAddress(),
			IncreaseAmount: curAmount.String(),
			Index:          0,
			Amount:         v.GetAmount(),
			Proof:          string(proofStr),
			MerkleRoot:     hexutil.Encode(merkleTree.MerkleRoot()),
			CreatedTime:    time.Now(),
		}

		if err := insertAirdropStats(airdropStats); err != nil {
			log.Errorf("insertMerkleTreeList err = %v", err)
			return err
		}
	}

	log.Info("insertMerkleTreeList end... ", common.NowTime())

	return nil
}

func InsertMerkleTreeV3(epoch int, epochDay, tokenTotal string, mpOrigin map[string]*model.Origin, merkleTree *smt.StandardTree) error {
	log.Info("insertAirdropTab begin... ", common.NowTime())
	if err := insertAirdropV2(epoch, epochDay, tokenTotal, merkleTree); err != nil {
		log.Errorf("insertAirdrop err = %v", err)
		return err
	}
	log.Info("insertAirdropTab end... ", common.NowTime())

	log.Info("insertMerkleTreeList begin... ", common.NowTime())

	index := 0
	var airdropStats []*model.AirdropStats

	for _, v := range mpOrigin {

		index++
		leaf := []interface{}{
			smt.SolAddress(v.BttcAddr),
			smt.SolNumber(v.Amount),
		}

		paths, err := util.GetMerklePath(merkleTree, leaf)
		if err != nil {
			log.Error("insertAirdropInfo GetMerklePath, err:", err)
			return err
		}
		proofStr, err := json.Marshal(paths)
		if err != nil {
			log.Error("insertAirdropInfo json.Marshal, err:", err)
			return err
		}

		curAmount := new(big.Int).Mul(big.NewInt(mpOrigin[v.BttcAddr].AmountBttOld), big.NewInt(priceRate))

		as := &model.AirdropStats{
			Epoch:          epoch,
			EpochDay:       epochDay,
			Level:          1,
			NodeId:         mpOrigin[v.BttcAddr].NodeId,
			BttcAddr:       v.BttcAddr,
			IncreaseAmount: curAmount.String(),
			Index:          0,
			Amount:         v.Amount,
			Proof:          string(proofStr),
			MerkleRoot:     hexutil.Encode(merkleTree.GetRoot()),
			CreatedTime:    time.Now(),
		}

		airdropStats = append(airdropStats, as)

		if index%10000 == 0 {
			if err := batchInsertAirdropStats(airdropStats); err != nil {
				log.Errorf("insertMerkleTreeList err = %v", err)
				return err
			}
			airdropStats = make([]*model.AirdropStats, 0)
		}
	}

	if len(airdropStats) > 0 {
		if err := batchInsertAirdropStats(airdropStats); err != nil {
			log.Errorf("insertMerkleTreeList err = %v", err)
			return err
		}
	}

	log.Info("insertMerkleTreeList end... ", common.NowTime())

	return nil
}

func GenerateMerkleTree(mpBalances map[string]int, epochDay string, index int, level string) (*MerkleTree, error) {
	// log.Info("read mpBalances:", mpBalances)
	// mpBalances = map[string]float64{"0c1a4c838099a06ffb384a87337de954dec160b2": 1, "7fdde5dcf8c09553d59b4daa55d39c7284c06682": 1}
	strBalances, err := json.Marshal(mpBalances)
	if err != nil {
		log.Errorf("json.Marshal(mpBalances) failed with %v", err)
		return nil, err
	}
	// log.Info("strBalances:", string(strBalances))

	cmd := exec.Command("pwd")
	path, err := cmd.CombinedOutput()
	if err != nil {
		log.Errorf("cmd.CombinedOutput() failed with %v", err)
		return nil, err
	}

	curPath := string(path)[:len(path)-1]
	outPath := curPath + "/../data_source/inter/"
	pathFile := curPath + "/../py/vecrv.py"
	log.Info("pathFile = ", pathFile)

	log.Info("write into file, begin ... ", common.NowTime())
	filename := epochDay + "." + strconv.Itoa(index) + "." + strconv.Itoa(rand.Intn(10000))
	err = common.WriteIntoFile(outPath+filename, strBalances)
	if err != nil {
		log.Errorf("write into file, failed, err:%v", err)
		return nil, err
	}
	log.Info("write into file, end ... ", common.NowTime())

	log.Infof("... cal merkleroot, cmd: \n python %v %v %v %v", pathFile, filename, outPath, level)

	cmd = exec.Command("python", pathFile, filename, outPath, level)
	out, err := cmd.CombinedOutput()
	if err != nil {
		log.Error("CombinedOutput err = ", err)
		return nil, err
	}
	log.Info("CombinedOutput out = ", string(out))

	log.Info("read from file, begin ... ", common.NowTime())
	outValue, err := common.ReadFromFile(outPath + filename + ".out")
	if err != nil {
		log.Errorf("read from file, failed, err:%v", err)
		return nil, err
	}
	log.Info("read from file, end ... ", common.NowTime())

	var merkleTree MerkleTree
	err = json.Unmarshal(outValue, &merkleTree)
	if err != nil {
		log.Errorf("insertInfo json.Unmarshal err = %v", err)
		return nil, err
	}

	return &merkleTree, nil
}

func GenerateMerkleTreeV2(merkleNodes []util.MerkleContent, epochDay string) (*util.MerkleTree, error) {
	return util.NewTreeWithHashStrategy(merkleNodes, sha3.NewLegacyKeccak256)
}

func GenerateMerkleTreeV3(merkleNodes []*util.Origin) (*smt.StandardTree, error) {
	return util.NewMerkleTree(merkleNodes)
}
