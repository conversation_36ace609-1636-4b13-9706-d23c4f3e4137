package job

import (
	"fmt"
	"log"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/bttc"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/config"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/dal"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/service"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/service/generate_merkle_tree"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/service/import_csv"
	util "gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/utils"
)

const (
	airdropJobExecuteTime   = "02:00:00"
	airdropFinanceCheckTime = "02:30:00"
)

func RunAirdrop() {
	s := gocron.NewScheduler(time.UTC)
	// _, err := s.Every(1).Day().At(config.BttcConfig.JobAirdropExecute).Do(func() {
	// AirDropJob(time.Now().AddDate(0, 0, -1))
	// })
	// if err != nil {
	// panic(err)
	// }

	_, err := s.Every(1).Day().At(config.BttcConfig.JobFinanceCheckExecute).Do(func() {
		service.FinanceCheck(config.BttcConfig.FinanceCheckRange, time.Now().AddDate(0, 0, -1))
	})
	if err != nil {
		panic(err)
	}

	_, err = s.Every(1).Hour().Do(func() {
		service.TransferEventMonitor()
	})

	s.StartBlocking()
}

func AirDropJob(date time.Time) {
	fmt.Println("Start airdrop job..............")
	epochDay := date.Format("2006-01-02")

	if a, err := dal.GetAirDropByDays([]string{epochDay}); err != nil {
		if err.Error() != "record not found" {
			log.Printf(fmt.Sprintf("%s %s, %s", "❌", epochDay, "Check if airdrop exist failed"))
			return
		}
	} else if a != nil && a[0].EpochDay == epochDay {
		log.Printf(fmt.Sprintf("%s %s, %s", "❌", epochDay, "Airdrop already exists"))
		return
	}

	err := import_csv.ImportCsv(config.FileConfig.CsvFilePath, epochDay)
	if err != nil {
		util.SendTelegramMessage(fmt.Sprintf("%s 导入 csv 文件到数据库失败, 失败原因：%s", "❌", err.Error()))
		return
	}
	util.SendTelegramMessage(fmt.Sprintf("%s %s, %s", "✅", epochDay, "Import csv success"))
	err = generate_merkle_tree.GenerateMerkleTree(epochDay)
	if err != nil {
		util.SendTelegramMessage(fmt.Sprintf("%s 生成 merkle tree失败, 失败原因：%s", "❌", err.Error()))
		return
	}
	util.SendTelegramMessage(fmt.Sprintf("%s %s, %s", "✅", epochDay, "Generate merkle tree success"))
	err = bttc.ProposalMerkleRoot(epochDay)
	if err != nil {
		util.SendTelegramMessage(fmt.Sprintf("%s propose merkle root 失败, 失败原因：%s", "❌", err.Error()))
		return
	}
	util.SendTelegramMessage(fmt.Sprintf("%s %s, %s", "✅", epochDay, "Propose merkle root success"))

	service.PreCheckMerkleProof(epochDay)

	fmt.Println("End airdrop job..............")
}
