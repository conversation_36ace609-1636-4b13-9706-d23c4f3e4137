from web3 import Web3
import sys
import json
from pathlib import Path
import time
from itertools import zip_longest
from collections import deque
from fractions import Fraction
from eth_abi.packed import encode_abi_packed
from eth_utils import encode_hex

BttSwitchRateWei = 1000000000000000000


class MerkleTree:
    def __init__(self, elements):
        self.elements = sorted(set(Web3.keccak(hexstr=el) for el in elements))
        self.layers = MerkleTree.get_layers(self.elements)

    @property
    def root(self):
        return self.layers[-1][0]

    def get_proof(self, el):
        el = Web3.keccak(hexstr=el)
        idx = self.elements.index(el)
        proof = []
        for layer in self.layers:
            pair_idx = idx + 1 if idx % 2 == 0 else idx - 1
            if pair_idx < len(layer):
                proof.append(encode_hex(layer[pair_idx]))
            idx //= 2
        return proof

    @staticmethod
    def get_layers(elements):
        layers = [elements]
        while len(layers[-1]) > 1:
            layers.append(MerkleTree.get_next_layer(layers[-1]))
        return layers

    @staticmethod
    def get_next_layer(elements):
        return [MerkleTree.combined_hash(a, b) for a, b in zip_longest(elements[::2], elements[1::2])]

    @staticmethod
    def combined_hash(a, b):
        if a is None:
            return b
        if b is None:
            return a
        return Web3.keccak(b''.join(sorted([a, b])))

def get_proof(balances, level):
    # # 1.airdrop total set btt, next will switch to wei
    # # total_distribution = 15000000
    # total_distribution = 500
    #
    # # 2.cal value, with Fraction
    # total_vecrv = sum(balances.values())
    # balances = {k: int(Fraction(v*total_distribution, total_vecrv)) for k, v in balances.items()}
    # balances = {k: v for k, v in balances.items() if v}
    #
    # # handle any rounding errors
    # addresses = deque(balances)
    # while sum(balances.values()) < total_distribution:
    #     balances[addresses[0]] += 1
    #     addresses.rotate()
    #
    # assert sum(balances.values()) == total_distribution

    # 3.switch btt to wei, to generate the MerkleTree
    balances = {k: v * BttSwitchRateWei for k, v in balances.items() if v}

    # elements = [(index, account, balances[account]) for index, account in enumerate(sorted(balances))]
    if level == '1':
        elements = [(index, account, balances[account]) for index, account in enumerate(sorted(balances))]
        nodes = [encode_abi_packed(['uint', 'address', 'uint'], el) for el in elements]
    else:
        elements = [(index, int(account, 16).to_bytes(32, byteorder="big"), balances[account]) for index, account in enumerate(sorted(balances))]
        nodes = [encode_abi_packed(['uint', 'bytes32', 'uint'], el) for el in elements]

    nodes = [encode_hex(el) for el in nodes]
    tree = MerkleTree(nodes)
    if level == '1':
        distribution = {
            'merkleRoot': encode_hex(tree.root),
            'tokenTotal': hex(sum(balances.values())),
            'claims': {
                user: {'index': index, 'amount': hex(amount), 'proof': tree.get_proof(nodes[index])}
                for index, user, amount in elements
            },
        }
    else:
        distribution = {
            'merkleRoot': encode_hex(tree.root),
            'tokenTotal': hex(sum(balances.values())),
            'claims': {
                '0x'+user.hex(): {'index': index, 'amount': hex(amount), 'proof': tree.get_proof(nodes[index])}
                for index, user, amount in elements
            },
        }
    # print(f'merkle root: {encode_hex(tree.root)}')
    return distribution


def main(balances, output_file, level):
    proof = get_proof(balances, level)
    # print("proof: ", proof)

    with output_file.open('w') as fp:
        json.dump(proof, fp)

    print(f"merkle tree saved to {output_file}")


# normal call it
if __name__ == "__main__":
    # in_filename = 'hello'
    # cur_path = '/Users/<USER>/go/src/gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/tools_local/../data_source/inter/'

    in_filename = sys.argv[1]
    cur_path = sys.argv[2]
    level = sys.argv[3]

    print("")
    print("py begin ...... ")
    print("py in_filename: ", in_filename)
    print("py cur_path: ", cur_path)

    distro_json = Path(cur_path + in_filename)
    with open(distro_json) as f:
        balances = json.load(f)

    if len(balances) > 0:
        output_file = Path(cur_path + in_filename + '.out')
        main(balances, output_file, level)

    print("py end ...... ")
    print("")


# # py test self
# if __name__ == "__main__":
#     # str_balances = '{"708e75096db0cc1b1f7daea0cee14a4c69a7c276": 1, "a614f803b6fd780986a42c78ec9c7f77e6ded13c": 1}'
#     # level = '1'
#
#     # str_balances = '{"0x0c4d6c84e061a2e36b94649124c38ab1d323e95995b16dcddc82fb72db2c1c1a":158633404863,"0x1173fb8876d6670c9eba3a3999fcfacc3872f82a3c3742c1ebc5e97bc345ae0e":280093881898,"0x1b7025a481c01b116877f1116e46add9ad0a4ed9e6bbe7ebe4179be483262549":263976343513,"0x201eafa8992edb46098a9ae625f48ee25ae20a88c331a1fd60a002fd36c6b70a":262463418404,"0x243fe303f0b8fd0cc61e3ae8f8b3bb4c60cb3686cc3bc3c19ff168d6bfcd7279":250983927067,"0x2ecfd443aa02ce41ee0766e64eb8f7453cb3eb4bca8eecf66386fff66aad5481":274486754320,"0x2ef4d1227825c16000bdd519f9e511407e0250b6ad85dd61261b1465a4eaaa22":264000110009,"0x30dd46a8fc4bfa1f9d9873379539cf3316a694623a5452df0d2b04580e289264":263927398870,"0x32dc8ab71ffc5a6bd21e0b32a8ec2cfa9b90d44ca41d4840410cd2797497e9f3":263489817127,"0x391d2da2c2586c7c891d1f729dff8c13587e43ba1f01dcf3d4615fb87b88502c":255793033333,"0x3db18d30aaef182b822cbad51d0bca7bf97596d23f94e485bf64aaf350a2ba1e":261299276004,"0x6060aaacfd1a0e6f226ae0ca0c365d1e69bb901102bc63ab00f760208f221fe3":252222274069,"0x63e76ddbbfb9d10f2b61f1dba1f565d8749f5cdb7c911638920787f83466bab3":263299271384,"0x6ed5f173d23a26ceebc46cb575d35900d757c352d66cc7c41f784ed09172da92":253451180775,"0x79c5392afa4aba186f784a592b77324c1a6f02ad90f3f37d18d96e244d546739":267890208047,"0x7c2fe9c5ef3bfa551190dff137d1c5e63c1b83389939b8c2f9ddc5e55e4c1201":274919498188,"0x86f39663b4daae93d2bcd4f0dadb363e5182f73175103b2190f7a83a6173d478":267332899708,"0x8b82b2b1125af78b50ae9d664149f2496fead7ebeb6fd82d5a697d02bbaf92e4":254151595213,"0x8e8318981cf2bdd043e5352980bd6e1da96150039e667c41686ae8fc1f85c831":252505883980,"0x91f79ea570fcf8abfc7c132603d78c9cc236c8cd8658fe5455552d2ac96ac005":263926904738,"0xa42f65d23a1983392fad63334aaf23c4feac5beb608b92c9e34a8a3b3db75e40":269123816799,"0xaaf611e3f333f903c69d4acdab36a574bdb81850a4f20ae61a44378d7d8b2dbb":261797406430,"0xb1d89ba69f029d0f023a832c7c8b5f29d651d96537cbb24bc798c9ea94e07098":267479017163,"0xb9483028680bc9495b7682bea7f176e52183717214968610169a3298b9542287":263126882097,"0xbf091a653d0626ff98d19d504043f47985ebcea3fd1a282a2cb2e6baf0b6814a":274062637868,"0xd27a50798d15490fdf53167557777fad409febbc0f01fa4459826bc45e494e2f":258259226440,"0xd31c6a0852bdf2baef221fa124f475d201c6f0deb576e7e9bb5c2bc75c537736":267536156841,"0xdf4571a1d69bd4b09a20520ee2962a3f326c8ddd3fc73d9141750d54ae69c655":278408331068,"0xea3dcf6d95a198e33f4afd6a6da7ee5305e4e04385e4a1c71a275fa5d0115c14":260169711059,"0xf3219276f72d0dc2d8076dd885ce724c0f0f6396a590798705db1728e7f5a12e":250658806481,"0xfd8d676cccf47907174debe6064bf206aae452cfc0fae6b19024cd8556c53992":275844203517}'
#     str_balances = '{"0x0c4d6c84e061a2e36b94649124c38ab1d323e95995b16dcddc82fb72db2c1c1a":1499999700}'
#     level = '2'
#
#     balances = json.loads(str_balances)
#     in_filename = 'hello'
#     cur_path = './'
#
#
#     print("py balances: ", balances)
#     print("py cur_path: ", cur_path)
#
#     if len(balances) > 0:
#         main(balances, Path(cur_path + in_filename + '.out'), level)
