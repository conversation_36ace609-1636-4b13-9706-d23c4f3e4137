package dal

import (
	"context"
	"fmt"

	"github.com/go-pg/pg"
	tapg "github.com/tron-us/go-common/v2/db/postgres"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/database/postgres"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/model"
)

func InsertAirdropTab(tab *model.Airdrop) error {
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		_, err = postgres.PGDBWrite.ModelContext(ctx, tab).
			OnConflict("(epoch) DO NOTHING").Insert()
	})

	return err
}

func InsertAirdropBatch(rows []*model.Airdrop) error {
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		_, err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			OnConflict("(node_id, epoch) DO NOTHING").Insert()
	})

	return err
}

func InsertAirdropStatsTab(row *model.AirdropStats) error {
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		_, err = postgres.PGDBWrite.ModelContext(ctx, row).
			OnConflict("(bttc_addr, epoch) DO NOTHING").Insert()
	})

	return err
}

func BatchInsertAirdropStatsTab(row []*model.AirdropStats) error {
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		_, err = postgres.PGDBWrite.ModelContext(ctx, &row).
			OnConflict("(bttc_addr, epoch) DO NOTHING").Insert()
	})

	return err
}

func IsExistAirdrop(epochDay string) (int, int, error) {
	// 1.get epoch from origin
	rows := []model.Origin{}
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			Where("epoch_day = ?", epochDay).
			Select()
	})
	if err != nil {
		return 0, 0, err
	}
	fmt.Println("... len(rows) = ", len(rows))
	epoch := rows[0].Epoch

	// 2.get count from airdrop
	rowsAirdrop := []model.Airdrop{}
	var count int
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		count, err = postgres.PGDBWrite.ModelContext(ctx, &rowsAirdrop).
			Where("epoch = ?", epoch).
			SelectAndCount()
	})
	if err != nil {
		return 0, 0, nil
	}

	return epoch, count, err
}

func GetAirDrop(epochDay string) (*model.Airdrop, error) {
	rows := []*model.Airdrop{}
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			Where("epoch_day = ?", epochDay).
			Select()
	})
	if err != nil {
		return nil, err
	}
	return rows[0], nil
}

func GetAirdropStats(epochDay string, batchSize, pageSize, pageNum int) ([]*model.AirdropStats, error) {
	start := (pageNum - 1) * pageSize
	limit := pageSize
	as := make([]*model.AirdropStats, 0)
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		err = postgres.PGDBRead.ModelContext(ctx, &as).
			Where("epoch_day = ?", epochDay).
			Order("created_time desc").
			Limit(limit).
			Offset(start).
			Select()
	})

	if err != nil {
		fmt.Println("GetAirdropStats Select, err:", err)
		return nil, err
	}

	return as, nil
}

func GetAirdropStatsCountByEpoch(epochDay string) (int, error) {
	var count int
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		count, err = postgres.PGDBRead.ModelContext(ctx, &model.AirdropStats{}).Where("epoch_day = ?", epochDay).Count()
	})

	if err != nil {
		fmt.Println("GetAirdropStatsCountByEpoch Count, err:", err)
		return 0, err
	}

	return count, nil
}

func GetAirDropByDays(epochDays []string) ([]*model.Airdrop, error) {
	var rows []*model.Airdrop
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			Where("epoch_day in (?)", pg.In(epochDays)).
			Select()
	})
	if err != nil {
		return nil, err
	}
	return rows, nil
}

func GetLatestAirdropDay() (*model.Airdrop, error) {
	var rows []*model.Airdrop
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			Order("id desc").
			Limit(1).
			Select()
	})
	if err != nil {
		return nil, err
	}
	return rows[0], nil
}
