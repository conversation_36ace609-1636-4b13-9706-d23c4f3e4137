package dal

import (
	"context"
	"fmt"

	"github.com/go-pg/pg"
	tapg "github.com/tron-us/go-common/v2/db/postgres"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/database/postgres"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/model"
)

func InsertOrigin(rows []*model.Origin) error {
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		_, err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			OnConflict("(node_id, epoch) DO NOTHING").OnConflict("(bttc_addr, epoch) DO NOTHING").Insert()
	})

	return err
}

func IsExistOrigin(epochDay string) (int, error) {
	rows := []model.Origin{}
	var count int
	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		count, err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			Where("epoch_day = ?", epochDay).
			SelectAndCount()
	})

	return count, err
}

// func GetMapBalances(epoch int) (mpBalances map[string]int, err error) {
//	rows := []model.Origin{}
//	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
//		err = postgres.PGDBWrite.ModelContext(ctx, &rows).
//			Column("bttc_addr").ColumnExpr("sum(amount_btt_old) as amount_btt_old").
//			Where("epoch <= ?", epoch).
//			Group("bttc_addr").
//			Order("bttc_addr").
//			Select()
//	})
//	if err != nil {
//		return nil, err
//	}
//
//	mpBalances = make(map[string]int)
//	for _, v := range rows {
//		mpBalances[v.BttcAddr] = int(v.AmountBttOld)
//	}
//
//	fmt.Println("GetMapBalances.length: ", len(mpBalances))
//
//	return mpBalances, nil
// }

func GetMapList(epoch int) (mpList []map[string]int, err error) {
	rows := []model.Origin{}
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			Column("bttc_addr").ColumnExpr("sum(amount_btt_old) as amount_btt_old").
			Where("epoch <= ?", epoch).
			Group("bttc_addr").
			Order("bttc_addr").
			Select()
	})
	if err != nil {
		return nil, err
	}

	mpBalances := make(map[string]int)
	for i, v := range rows {
		mpBalances[v.BttcAddr] = int(v.AmountBttOld)

		if i > 0 && i%10000 == 0 {
			mpList = append(mpList, mpBalances)
			mpBalances = make(map[string]int)
		}
	}
	if len(mpBalances) > 0 {
		mpList = append(mpList, mpBalances)
	}

	fmt.Println("mpList.length: ", len(mpList))

	return mpList, nil
}

func GetMapListV2(epoch int) (origins []*model.Origin, err error) {
	rows := make([]*model.Origin, 0)
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			Column("bttc_addr").ColumnExpr("sum(amount_btt_old) as amount_btt_old").
			Where("epoch <= ?", epoch).
			Group("bttc_addr").
			Order("bttc_addr").
			Select()
	})
	if err != nil {
		return nil, err
	}

	return rows, nil
}

func GetMapListV2WithPage(epoch int, addresses []string) (origins []*model.Origin, err error) {
	// 将 address 进行分批查询
	batchSize := 1000
	var allRows []*model.Origin

	for i := 0; i < len(addresses); i += batchSize {
		end := i + batchSize
		if end > len(addresses) {
			end = len(addresses)
		}
		batchAddress := addresses[i:end]

		rows := make([]*model.Origin, 0)
		var tempErr error
		tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
			tempErr = postgres.PGDBWrite.ModelContext(ctx, &rows).
				Column("bttc_addr").ColumnExpr("sum(amount_btt_old) as amount_btt_old").
				Where("epoch <= ?", epoch).
				Where("bttc_addr IN (?)", pg.In(batchAddress)).
				Group("bttc_addr").
				Select()
		})

		if tempErr != nil {
			return nil, tempErr
		}

		if len(rows) == 0 {
			break
		}

		allRows = append(allRows, rows...)
	}

	return allRows, nil
}

func GetTotalAmount(epoch int) (origins []*model.Origin, err error) {
	rows := make([]*model.Origin, 0)
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			Column("bttc_addr").ColumnExpr("sum(amount_btt_old) as amount_btt_old").
			Where("epoch = ?", epoch).
			Group("bttc_addr").
			Order("bttc_addr").
			Select()
	})
	if err != nil {
		return nil, err
	}

	return rows, nil
}

func GetTotalAmountWithPage(epoch int) (origins []*model.Origin, err error) {
	pageSize := 10000
	var allRows []*model.Origin

	for offset := 0; ; offset += pageSize {
		rows := make([]*model.Origin, 0)
		var tempErr error
		tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
			tempErr = postgres.PGDBWrite.ModelContext(ctx, &rows).
				Column("bttc_addr").
				ColumnExpr("sum(amount_btt_old) as amount_btt_old").
				Where("epoch = ?", epoch).
				Group("bttc_addr").
				Order("bttc_addr").
				Limit(pageSize).
				Offset(offset).
				Select()
		})

		if tempErr != nil {
			return nil, tempErr
		}

		if len(rows) == 0 {
			break
		}

		allRows = append(allRows, rows...)
	}
	fmt.Println("origin length = ", len(allRows))
	return allRows, nil
}

// GetMapCurAmount : get currency map amount.
func GetMapCurAmount(epoch int) (mpOrigin map[string]*model.Origin, err error) {
	rows := []model.Origin{}
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		err = postgres.PGDBWrite.ModelContext(ctx, &rows).
			Where("epoch = ?", epoch).
			Select()
	})
	if err != nil {
		return nil, err
	}

	// fmt.Printf("rows = %+v \n ", rows)

	mpOrigin = make(map[string]*model.Origin)
	for i, v := range rows {
		mpOrigin[v.BttcAddr] = &rows[i]
	}

	fmt.Println("mpOrigin.length: ", len(mpOrigin))

	return mpOrigin, nil
}

// GetMapCurAmountWithPage : get currency map amount.
func GetMapCurAmountWithPage(epoch int) (mpOrigin map[string]*model.Origin, err error) {
	pageSize := 10000
	mpOrigin = make(map[string]*model.Origin)

	for offset := 0; ; offset += pageSize {
		rows := make([]*model.Origin, 0)
		var tempErr error

		tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
			tempErr = postgres.PGDBWrite.ModelContext(ctx, &rows).
				Where("epoch = ?", epoch).
				Order("id").
				Limit(pageSize).
				Offset(offset).
				Select()
		})

		if tempErr != nil {
			return nil, tempErr
		}

		// 如果没有更多数据，退出循环
		if len(rows) == 0 {
			break
		}

		// 将当前页的数据添加到结果map中
		for i, v := range rows {
			mpOrigin[v.BttcAddr] = rows[i]
		}
	}

	fmt.Println("mpOrigin.length: ", len(mpOrigin))
	return mpOrigin, nil
}

func OriginListPaginate(epoch int) ([]*model.Origin, error) {
	pageSize := 10000
	var allRows []*model.Origin

	for offset := 0; ; offset += pageSize {
		rows := make([]*model.Origin, 0)
		var tempErr error
		tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
			tempErr = postgres.PGDBWrite.ModelContext(ctx, &rows).
				Where("epoch = ?", epoch).
				Order("id").
				Limit(pageSize).
				Offset(offset).
				Select()
		})

		if tempErr != nil {
			return nil, tempErr
		}

		if len(rows) == 0 {
			break
		}

		allRows = append(allRows, rows...)
	}

	return allRows, nil
}

func GetTotalAmountByEpoch(epoch int) (uint64, error) {
	var tempErr error
	var totalAmount int64

	m := model.Origin{}
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		tempErr = postgres.PGDBWrite.ModelContext(ctx, &m).
			ColumnExpr("sum(amount_btt_old) as amount_btt_old").
			Where("epoch = ?", epoch).
			Select()
	})
	if tempErr != nil {
		fmt.Println("Error getting total amount:", tempErr)
		return 0, tempErr
	}
	totalAmount = m.AmountBttOld
	return uint64(totalAmount), nil
}
