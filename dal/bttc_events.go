package dal

import (
	"context"
	"strings"

	"github.com/go-pg/pg"
	tapg "github.com/tron-us/go-common/v2/db/postgres"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/database/postgres"
	"gitlab.insidebt.net/btfs/go-btfs-backend-airdrop/model"
)

func ListClaimedEvents(pageSize, pageNum int, airdropContractAddr string, days []string) ([]*model.BttcEvents, error) {
	start := (pageNum - 1) * pageSize
	limit := pageSize

	events := make([]*model.BttcEvents, 0)

	var err error
	tapg.WithContextTimeout(context.Background(), func(ctx context.Context) {
		query := postgres.PGDBWrite.ModelContext(ctx, &events).
			Where("name = ? and contract_addr = ? and block_date in (?) ", "Claimed", strings.ToLower(airdropContractAddr), pg.In(days))
		err = query.Order("id desc").Offset(start).Limit(limit).Select()
	})
	return events, err
}
